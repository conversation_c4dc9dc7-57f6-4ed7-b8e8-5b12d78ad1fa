<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>编辑器</title>
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
</head>
<body>
    <div class="editor-container">
        <!-- 顶部菜单栏 -->
        <div class="menu-bar">
            <div class="app-logo">
                <i class="fas fa-code"></i>
                <span class="logo-text">YS Code Editor</span>
            </div>
            <div class="menu-items">
                <div class="menu-item" data-menu="file">
                    <span>文件(F)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="new-file">
                            <span>新建文件</span>
                            <span class="shortcut">Ctrl+N</span>
                        </div>
                        <div class="menu-option" data-action="open-file">
                            <span>打开文件</span>
                            <span class="shortcut">Ctrl+O</span>
                        </div>
                        <div class="menu-separator"></div>
                        <div class="menu-option" data-action="save">
                            <span>保存</span>
                            <span class="shortcut">Ctrl+S</span>
                        </div>
                        <div class="menu-option" data-action="save-as">
                            <span>另存为</span>
                            <span class="shortcut">Ctrl+Shift+S</span>
                        </div>
                        <div class="menu-separator"></div>
                        <div class="menu-option" data-action="close-file">
                            <span>关闭文件</span>
                            <span class="shortcut">Ctrl+W</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="edit">
                    <span>编辑(E)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="undo">
                            <span>撤销</span>
                            <span class="shortcut">Ctrl+Z</span>
                        </div>
                        <div class="menu-option" data-action="redo">
                            <span>重做</span>
                            <span class="shortcut">Ctrl+Y</span>
                        </div>
                        <div class="menu-separator"></div>
                        <div class="menu-option" data-action="cut">
                            <span>剪切</span>
                            <span class="shortcut">Ctrl+X</span>
                        </div>
                        <div class="menu-option" data-action="copy">
                            <span>复制</span>
                            <span class="shortcut">Ctrl+C</span>
                        </div>
                        <div class="menu-option" data-action="paste">
                            <span>粘贴</span>
                            <span class="shortcut">Ctrl+V</span>
                        </div>
                        <div class="menu-separator"></div>
                        <div class="menu-option" data-action="find">
                            <span>查找</span>
                            <span class="shortcut">Ctrl+F</span>
                        </div>
                        <div class="menu-option" data-action="replace">
                            <span>替换</span>
                            <span class="shortcut">Ctrl+H</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="selection">
                    <span>选择(S)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="select-all">
                            <span>全选</span>
                            <span class="shortcut">Ctrl+A</span>
                        </div>
                        <div class="menu-option" data-action="select-line">
                            <span>选择行</span>
                            <span class="shortcut">Ctrl+L</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="view">
                    <span>查看(V)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="toggle-sidebar">
                            <span>切换侧边栏</span>
                            <span class="shortcut">Ctrl+B</span>
                        </div>
                        <div class="menu-option" data-action="toggle-panel">
                            <span>切换面板</span>
                            <span class="shortcut">Ctrl+J</span>
                        </div>
                        <div class="menu-separator"></div>
                        <div class="menu-option" data-action="zoom-in">
                            <span>放大</span>
                            <span class="shortcut">Ctrl++</span>
                        </div>
                        <div class="menu-option" data-action="zoom-out">
                            <span>缩小</span>
                            <span class="shortcut">Ctrl+-</span>
                        </div>
                        <div class="menu-option" data-action="reset-zoom">
                            <span>重置缩放</span>
                            <span class="shortcut">Ctrl+0</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="go">
                    <span>转到(G)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="go-to-line">
                            <span>转到行</span>
                            <span class="shortcut">Ctrl+G</span>
                        </div>
                        <div class="menu-option" data-action="go-to-file">
                            <span>转到文件</span>
                            <span class="shortcut">Ctrl+P</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="run">
                    <span>运行(R)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="run-file">
                            <span>运行文件</span>
                            <span class="shortcut">F5</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="terminal">
                    <span>终端(T)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="new-terminal">
                            <span>新建终端</span>
                            <span class="shortcut">Ctrl+Shift+`</span>
                        </div>
                    </div>
                </div>
                <div class="menu-item" data-menu="help">
                    <span>帮助(H)</span>
                    <div class="dropdown-menu">
                        <div class="menu-option" data-action="about">
                            <span>关于</span>
                        </div>
                        <div class="menu-option" data-action="shortcuts">
                            <span>键盘快捷键</span>
                            <span class="shortcut">Ctrl+K Ctrl+S</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="window-controls">
                <span class="control minimize"><i class="fas fa-minus"></i></span>
                <span class="control maximize"><i class="fas fa-square"></i></span>
                <span class="control close"><i class="fas fa-times"></i></span>
            </div>
        </div>

        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- 侧边栏遮罩（移动端） -->
            <div class="sidebar-overlay"></div>

            <!-- 活动栏 -->
            <div class="activity-bar">
                <div class="activity-item active" data-panel="explorer">
                    <i class="fas fa-folder"></i>
                </div>
                <div class="activity-item" data-panel="search">
                    <i class="fas fa-search"></i>
                </div>
                <div class="activity-item" data-panel="git">
                    <i class="fas fa-code-branch"></i>
                </div>
                <div class="activity-item" data-panel="debug">
                    <i class="fas fa-bug"></i>
                </div>
                <div class="activity-item" data-panel="extensions">
                    <i class="fas fa-th-large"></i>
                </div>
                <div class="activity-item" data-panel="ai-assistant">
                    <i class="fas fa-robot"></i>
                </div>
            </div>

            <!-- 侧边栏 -->
            <div class="sidebar">
                <!-- 资源管理器面板 -->
                <div class="panel explorer-panel active">
                    <div class="panel-header">
                        <span class="panel-title">资源管理器</span>
                        <div class="panel-actions">
                            <i class="fas fa-file-plus" title="新建文件"></i>
                            <i class="fas fa-folder-plus" title="新建文件夹"></i>
                            <i class="fas fa-sync-alt" title="刷新"></i>
                        </div>
                    </div>
                    <div class="file-tree">
                        <div class="tree-item folder-item" data-path="project" data-expanded="true">
                            <div class="tree-item-content">
                                <i class="fas fa-chevron-down expand-icon"></i>
                                <i class="fas fa-folder-open folder-icon"></i>
                                <span class="item-name">测试项目</span>
                            </div>
                            <div class="tree-children">
                                <div class="tree-item file-item" data-path="project/index.html" data-file="index.html">
                                    <div class="tree-item-content">
                                        <i class="fab fa-html5 file-icon"></i>
                                        <span class="item-name">index.html</span>
                                    </div>
                                </div>
                                <div class="tree-item file-item" data-path="project/styles.css" data-file="styles.css">
                                    <div class="tree-item-content">
                                        <i class="fab fa-css3-alt file-icon"></i>
                                        <span class="item-name">styles.css</span>
                                    </div>
                                </div>
                                <div class="tree-item file-item" data-path="project/script.js" data-file="script.js">
                                    <div class="tree-item-content">
                                        <i class="fab fa-js-square file-icon"></i>
                                        <span class="item-name">script.js</span>
                                    </div>
                                </div>
                                <div class="tree-item folder-item" data-path="project/assets" data-expanded="false">
                                    <div class="tree-item-content">
                                        <i class="fas fa-chevron-right expand-icon"></i>
                                        <i class="fas fa-folder folder-icon"></i>
                                        <span class="item-name">assets</span>
                                    </div>
                                    <div class="tree-children" style="display: none;">
                                        <div class="tree-item folder-item" data-path="project/assets/css" data-expanded="false">
                                            <div class="tree-item-content">
                                                <i class="fas fa-chevron-right expand-icon"></i>
                                                <i class="fas fa-folder folder-icon"></i>
                                                <span class="item-name">css</span>
                                            </div>
                                            <div class="tree-children" style="display: none;">
                                                <div class="tree-item file-item" data-path="project/assets/css/main.css" data-file="main.css">
                                                    <div class="tree-item-content">
                                                        <i class="fab fa-css3-alt file-icon"></i>
                                                        <span class="item-name">main.css</span>
                                                    </div>
                                                </div>
                                                <div class="tree-item file-item" data-path="project/assets/css/reset.css" data-file="reset.css">
                                                    <div class="tree-item-content">
                                                        <i class="fab fa-css3-alt file-icon"></i>
                                                        <span class="item-name">reset.css</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tree-item folder-item" data-path="project/assets/js" data-expanded="false">
                                            <div class="tree-item-content">
                                                <i class="fas fa-chevron-right expand-icon"></i>
                                                <i class="fas fa-folder folder-icon"></i>
                                                <span class="item-name">js</span>
                                            </div>
                                            <div class="tree-children" style="display: none;">
                                                <div class="tree-item file-item" data-path="project/assets/js/utils.js" data-file="utils.js">
                                                    <div class="tree-item-content">
                                                        <i class="fab fa-js-square file-icon"></i>
                                                        <span class="item-name">utils.js</span>
                                                    </div>
                                                </div>
                                                <div class="tree-item file-item" data-path="project/assets/js/config.js" data-file="config.js">
                                                    <div class="tree-item-content">
                                                        <i class="fab fa-js-square file-icon"></i>
                                                        <span class="item-name">config.js</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="tree-item folder-item" data-path="project/assets/images" data-expanded="false">
                                            <div class="tree-item-content">
                                                <i class="fas fa-chevron-right expand-icon"></i>
                                                <i class="fas fa-folder folder-icon"></i>
                                                <span class="item-name">images</span>
                                            </div>
                                            <div class="tree-children" style="display: none;">
                                                <div class="tree-item file-item" data-path="project/assets/images/logo.png" data-file="logo.png">
                                                    <div class="tree-item-content">
                                                        <i class="fas fa-image file-icon"></i>
                                                        <span class="item-name">logo.png</span>
                                                    </div>
                                                </div>
                                                <div class="tree-item file-item" data-path="project/assets/images/icon.svg" data-file="icon.svg">
                                                    <div class="tree-item-content">
                                                        <i class="fas fa-image file-icon"></i>
                                                        <span class="item-name">icon.svg</span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-item folder-item" data-path="project/docs" data-expanded="false">
                                    <div class="tree-item-content">
                                        <i class="fas fa-chevron-right expand-icon"></i>
                                        <i class="fas fa-folder folder-icon"></i>
                                        <span class="item-name">docs</span>
                                    </div>
                                    <div class="tree-children" style="display: none;">
                                        <div class="tree-item file-item" data-path="project/docs/README.md" data-file="README.md">
                                            <div class="tree-item-content">
                                                <i class="fab fa-markdown file-icon"></i>
                                                <span class="item-name">README.md</span>
                                            </div>
                                        </div>
                                        <div class="tree-item file-item" data-path="project/docs/CHANGELOG.md" data-file="CHANGELOG.md">
                                            <div class="tree-item-content">
                                                <i class="fab fa-markdown file-icon"></i>
                                                <span class="item-name">CHANGELOG.md</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="tree-item file-item" data-path="project/package.json" data-file="package.json">
                                    <div class="tree-item-content">
                                        <i class="fas fa-code file-icon"></i>
                                        <span class="item-name">package.json</span>
                                    </div>
                                </div>
                                <div class="tree-item file-item" data-path="project/test.html" data-file="test.html">
                                    <div class="tree-item-content">
                                        <i class="fab fa-html5 file-icon"></i>
                                        <span class="item-name">test.html</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 搜索面板 -->
                <div class="panel search-panel">
                    <div class="panel-header">
                        <span class="panel-title">搜索</span>
                    </div>
                    <div class="search-content">
                        <input type="text" placeholder="搜索" class="search-input">
                        <input type="text" placeholder="替换" class="replace-input">
                    </div>
                </div>

                <!-- Git面板 -->
                <div class="panel git-panel">
                    <div class="panel-header">
                        <span class="panel-title">源代码管理</span>
                    </div>
                    <div class="git-content">
                        <p>没有活动的源代码管理提供程序。</p>
                    </div>
                </div>

                <!-- AI代码助手面板 -->
                <div class="panel ai-assistant-panel">
                    <div class="panel-header">
                        <span class="panel-title">AI代码助手</span>
                        <div class="panel-actions">
                            <i class="fas fa-sync-alt" title="刷新对话"></i>
                            <i class="fas fa-trash" title="清空对话"></i>
                        </div>
                    </div>
                    <div class="ai-assistant-content">
                        <!-- AI助手对话区域 -->
                        <div class="ai-chat-container">
                            <div class="ai-chat-messages" id="aiChatMessages">
                                <div class="ai-message ai-system">
                                    <div class="ai-avatar">
                                        <i class="fas fa-robot"></i>
                                    </div>
                                    <div class="ai-content">
                                        <p>你好！我是YS Code Editor的AI代码助手。我可以帮助您：</p>
                                        <ul>
                                            <li>🔍 分析和解释代码</li>
                                            <li>🛠️ 生成代码片段</li>
                                            <li>🐛 调试和修复错误</li>
                                            <li>📝 优化代码性能</li>
                                            <li>💡 提供编程建议</li>
                                        </ul>
                                        <p>请告诉我您需要什么帮助！</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 快捷操作按钮 -->
                            <div class="ai-quick-actions">
                                <button class="ai-action-btn" data-action="explain-code">
                                    <i class="fas fa-lightbulb"></i>
                                    解释代码
                                </button>
                                <button class="ai-action-btn" data-action="optimize-code">
                                    <i class="fas fa-rocket"></i>
                                    优化代码
                                </button>
                                <button class="ai-action-btn" data-action="find-bugs">
                                    <i class="fas fa-bug"></i>
                                    查找错误
                                </button>
                                <button class="ai-action-btn" data-action="generate-docs">
                                    <i class="fas fa-file-alt"></i>
                                    生成文档
                                </button>
                            </div>

                            <!-- AI助手输入区域 -->
                            <div class="ai-input-container">
                                <div class="ai-input-wrapper">
                                    <textarea
                                        class="ai-input"
                                        id="aiInput"
                                        placeholder="请输入您的问题或需要帮助的代码..."
                                        rows="3"
                                    ></textarea>
                                    <div class="ai-input-actions">
                                        <button class="ai-send-btn" id="aiSendBtn">
                                            <i class="fas fa-paper-plane"></i>
                                        </button>
                                        <button class="ai-attach-btn" id="aiAttachBtn" title="附加当前文件">
                                            <i class="fas fa-paperclip"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 编辑器区域 -->
            <div class="editor-area">
                <!-- 编辑器主体 -->
                <div class="editor-main">
                <!-- 标签栏 -->
                <div class="tab-bar">
                    <div class="tab active" data-file="welcome">
                        <i class="fas fa-home"></i>
                        <span class="tab-name">欢迎</span>
                        <i class="fas fa-times tab-close"></i>
                    </div>
                </div>

                <!-- 编辑器内容 -->
                <div class="editor-content">
                    <!-- 欢迎页面 -->
                    <div class="welcome-page editor-tab active" id="welcomePage" data-file="welcome">
                        <div class="welcome-container">
                            <div class="welcome-header">
                                <h1 class="welcome-title">
                                    <i class="fas fa-code"></i>
                                    YS Code Editor
                                </h1>
                                <p class="welcome-subtitle">专业的代码编辑器，为开发者而生</p>
                            </div>

                            <div class="welcome-content">
                                <div class="welcome-section">
                                    <h2>开始</h2>
                                    <div class="action-grid">
                                        <div class="action-item" data-action="new-file">
                                            <i class="fas fa-plus"></i>
                                            <span>新建文件</span>
                                            <small>Ctrl+N</small>
                                        </div>
                                        <div class="action-item" data-action="open-file">
                                            <i class="fas fa-folder-open"></i>
                                            <span>打开文件</span>
                                            <small>Ctrl+O</small>
                                        </div>
                                        <div class="action-item" data-action="open-folder">
                                            <i class="fas fa-folder"></i>
                                            <span>打开文件夹</span>
                                            <small>Ctrl+K Ctrl+O</small>
                                        </div>
                                        <div class="action-item" data-action="clone-repo">
                                            <i class="fas fa-download"></i>
                                            <span>克隆仓库</span>
                                            <small>Git Clone</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="welcome-section">
                                    <h2>最近</h2>
                                    <div class="recent-files" id="recentFiles">
                                        <div class="recent-item" data-file="index.html">
                                            <i class="fab fa-html5"></i>
                                            <div class="recent-info">
                                                <span class="recent-name">index.html</span>
                                                <span class="recent-path">project/index.html</span>
                                            </div>
                                            <span class="recent-time">2分钟前</span>
                                        </div>
                                        <div class="recent-item" data-file="styles.css">
                                            <i class="fab fa-css3-alt"></i>
                                            <div class="recent-info">
                                                <span class="recent-name">styles.css</span>
                                                <span class="recent-path">project/styles.css</span>
                                            </div>
                                            <span class="recent-time">5分钟前</span>
                                        </div>
                                        <div class="recent-item" data-file="script.js">
                                            <i class="fab fa-js-square"></i>
                                            <div class="recent-info">
                                                <span class="recent-name">script.js</span>
                                                <span class="recent-path">project/script.js</span>
                                            </div>
                                            <span class="recent-time">10分钟前</span>
                                        </div>
                                        <div class="recent-item" data-file="README.md">
                                            <i class="fab fa-markdown"></i>
                                            <div class="recent-info">
                                                <span class="recent-name">README.md</span>
                                                <span class="recent-path">project/README.md</span>
                                            </div>
                                            <span class="recent-time">1小时前</span>
                                        </div>
                                    </div>
                                    <div class="recent-actions">
                                        <button class="recent-action-btn" data-action="clear-recent">
                                            <i class="fas fa-trash"></i>
                                            清除最近文件
                                        </button>
                                        <button class="recent-action-btn" data-action="more-recent">
                                            <i class="fas fa-ellipsis-h"></i>
                                            更多...
                                        </button>
                                    </div>
                                </div>

                                <div class="welcome-section">
                                    <h2>帮助</h2>
                                    <div class="help-links">
                                        <a href="#" class="help-link" data-action="show-shortcuts">
                                            <i class="fas fa-keyboard"></i>
                                            <span>键盘快捷键</span>
                                        </a>
                                        <a href="#" class="help-link" data-action="show-docs">
                                            <i class="fas fa-book"></i>
                                            <span>使用文档</span>
                                        </a>
                                        <a href="#" class="help-link" data-action="show-tips">
                                            <i class="fas fa-lightbulb"></i>
                                            <span>提示和技巧</span>
                                        </a>
                                        <a href="#" class="help-link" data-action="show-about">
                                            <i class="fas fa-info-circle"></i>
                                            <span>关于编辑器</span>
                                        </a>
                                    </div>
                                </div>
                            </div>

                            <div class="welcome-footer">
                                <p>版本 1.0.0 | <a href="#" data-action="show-changelog">更新日志</a> | <a href="#" data-action="show-settings">设置</a></p>
                            </div>
                        </div>
                    </div>

                    <!-- 代码编辑器 -->
                    <div class="code-editor-container" id="codeEditorContainer" style="display: none;">
                        <div class="line-numbers">
                            <div class="line-number">1</div>
                            <div class="line-number">2</div>
                            <div class="line-number">3</div>
                            <div class="line-number">4</div>
                            <div class="line-number">5</div>
                            <div class="line-number">6</div>
                            <div class="line-number">7</div>
                            <div class="line-number">8</div>
                            <div class="line-number">9</div>
                            <div class="line-number">10</div>
                        </div>
                        <textarea class="code-editor" placeholder="开始编写代码..."></textarea>
                    </div>
                </div>
                </div>

                <!-- 分割条 -->
                <div class="terminal-splitter" id="terminalSplitter">
                    <div class="splitter-line"></div>
                </div>

                <!-- 终端面板 -->
                <div class="terminal-panel" id="terminalPanel">
                    <div class="terminal-header">
                        <div class="terminal-tabs">
                            <div class="terminal-tab active" data-terminal="1">
                                <i class="fas fa-terminal"></i>
                                <span>终端 1</span>
                                <i class="fas fa-times terminal-tab-close"></i>
                            </div>
                        </div>
                        <div class="terminal-actions">
                            <button class="terminal-action" id="newTerminal" title="新建终端">
                                <i class="fas fa-plus"></i>
                            </button>
                            <button class="terminal-action" id="clearTerminal" title="清空终端">
                                <i class="fas fa-trash"></i>
                            </button>
                            <button class="terminal-action" id="toggleTerminal" title="隐藏终端">
                                <i class="fas fa-chevron-down"></i>
                            </button>
                        </div>
                    </div>
                    <div class="terminal-content">
                        <div class="terminal-output" id="terminalOutput">
                            <div class="terminal-line">
                                <span class="terminal-prompt">YS Code Editor Terminal</span>
                            </div>
                            <div class="terminal-line">
                                <span class="terminal-prompt">$ </span>
                                <span class="terminal-cursor">|</span>
                            </div>
                        </div>
                        <div class="terminal-input-container">
                            <span class="terminal-prompt">$ </span>
                            <input type="text" class="terminal-input" id="terminalInput" placeholder="输入命令..." autocomplete="off">
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 状态栏 -->
        <div class="status-bar">
            <div class="status-left">
                <span class="status-item">
                    <i class="fas fa-code-branch"></i>
                    main
                </span>
                <span class="status-item">
                    <i class="fas fa-exclamation-triangle"></i>
                    0
                </span>
                <span class="status-item">
                    <i class="fas fa-times-circle"></i>
                    0
                </span>
            </div>
            <div class="status-right">
                <span class="status-item">UTF-8</span>
                <span class="status-item">LF</span>
                <span class="status-item">HTML</span>
                <span class="status-item">第 1 行，第 1 列</span>
            </div>
        </div>
    </div>

    <!-- 文件树右键菜单 -->
    <div class="context-menu" id="contextMenu">
        <div class="context-menu-item" data-action="new-file">
            <i class="fas fa-file-plus"></i>
            <span>新建文件</span>
        </div>
        <div class="context-menu-item" data-action="new-folder">
            <i class="fas fa-folder-plus"></i>
            <span>新建文件夹</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="rename">
            <i class="fas fa-edit"></i>
            <span>重命名</span>
        </div>
        <div class="context-menu-item" data-action="delete">
            <i class="fas fa-trash"></i>
            <span>删除</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="copy-path">
            <i class="fas fa-copy"></i>
            <span>复制路径</span>
        </div>
    </div>

    <!-- 标签页右键菜单 -->
    <div class="context-menu" id="tabContextMenu">
        <div class="context-menu-item" data-action="close-current">
            <i class="fas fa-times"></i>
            <span>关闭当前标签</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="close-others">
            <i class="fas fa-times-circle"></i>
            <span>关闭其他标签</span>
        </div>
        <div class="context-menu-item" data-action="close-left">
            <i class="fas fa-arrow-left"></i>
            <span>关闭左边标签</span>
        </div>
        <div class="context-menu-item" data-action="close-right">
            <i class="fas fa-arrow-right"></i>
            <span>关闭右边标签</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="close-all">
            <i class="fas fa-ban"></i>
            <span>关闭所有标签</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="copy-file-path">
            <i class="fas fa-copy"></i>
            <span>复制文件路径</span>
        </div>
        <div class="context-menu-item" data-action="reveal-in-explorer">
            <i class="fas fa-folder-open"></i>
            <span>在资源管理器中显示</span>
        </div>
    </div>

    <!-- 代码编辑器右键菜单 -->
    <div class="context-menu" id="editorContextMenu">
        <div class="context-menu-item" data-action="undo">
            <i class="fas fa-undo"></i>
            <span>撤销</span>
            <span class="shortcut">Ctrl+Z</span>
        </div>
        <div class="context-menu-item" data-action="redo">
            <i class="fas fa-redo"></i>
            <span>重做</span>
            <span class="shortcut">Ctrl+Y</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="cut">
            <i class="fas fa-cut"></i>
            <span>剪切</span>
            <span class="shortcut">Ctrl+X</span>
        </div>
        <div class="context-menu-item" data-action="copy">
            <i class="fas fa-copy"></i>
            <span>复制</span>
            <span class="shortcut">Ctrl+C</span>
        </div>
        <div class="context-menu-item" data-action="paste">
            <i class="fas fa-paste"></i>
            <span>粘贴</span>
            <span class="shortcut">Ctrl+V</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="select-all">
            <i class="fas fa-check-square"></i>
            <span>全选</span>
            <span class="shortcut">Ctrl+A</span>
        </div>
        <div class="context-menu-item" data-action="select-line">
            <i class="fas fa-minus"></i>
            <span>选择行</span>
            <span class="shortcut">Ctrl+L</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="find">
            <i class="fas fa-search"></i>
            <span>查找</span>
            <span class="shortcut">Ctrl+F</span>
        </div>
        <div class="context-menu-item" data-action="replace">
            <i class="fas fa-exchange-alt"></i>
            <span>替换</span>
            <span class="shortcut">Ctrl+H</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="go-to-line">
            <i class="fas fa-arrow-right"></i>
            <span>转到行</span>
            <span class="shortcut">Ctrl+G</span>
        </div>
        <div class="context-menu-separator"></div>
        <div class="context-menu-item" data-action="toggle-comment">
            <i class="fas fa-comment"></i>
            <span>切换注释</span>
            <span class="shortcut">Ctrl+/</span>
        </div>
        <div class="context-menu-item" data-action="format-document">
            <i class="fas fa-align-left"></i>
            <span>格式化文档</span>
            <span class="shortcut">Shift+Alt+F</span>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
