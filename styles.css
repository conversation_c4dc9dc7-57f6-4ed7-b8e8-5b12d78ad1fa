/* VS Code 风格编辑器样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #1e1e1e;
    color: #cccccc;
    overflow: hidden;
}

.editor-container {
    height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 顶部菜单栏 */
.menu-bar {
    background-color: #323233;
    height: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    border-bottom: 1px solid #2d2d30;
}

.menu-items {
    display: flex;
    gap: 0;
}

.menu-item {
    position: relative;
    font-size: 13px;
    padding: 5px 12px;
    cursor: pointer;
    border-radius: 3px;
    transition: background-color 0.2s;
    user-select: none;
}

.menu-item:hover {
    background-color: #3c3c3c;
}

.menu-item.active {
    background-color: #094771;
}

/* 下拉菜单样式 */
.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    background-color: #2d2d30;
    border: 1px solid #464647;
    border-radius: 3px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 200px;
    z-index: 1000;
    opacity: 0;
    visibility: hidden;
    transform: translateY(-5px);
    transition: all 0.2s ease;
}

.menu-item:hover .dropdown-menu,
.menu-item.active .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.menu-option {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 16px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.menu-option:hover {
    background-color: #094771;
}

.menu-option:first-child {
    border-radius: 3px 3px 0 0;
}

.menu-option:last-child {
    border-radius: 0 0 3px 3px;
}

.shortcut {
    color: #858585;
    font-size: 11px;
    margin-left: 20px;
}

.menu-separator {
    height: 1px;
    background-color: #464647;
    margin: 4px 0;
}

.window-controls {
    display: flex;
    gap: 5px;
}

.control {
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 10px;
    border-radius: 3px;
}

.control:hover {
    background-color: #3c3c3c;
}

.control.close:hover {
    background-color: #e81123;
    color: white;
}

/* 主要内容区域 */
.main-content {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 活动栏 */
.activity-bar {
    width: 48px;
    background-color: #333333;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 0;
    border-right: 1px solid #2d2d30;
}

.activity-item {
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    margin-bottom: 10px;
    border-radius: 5px;
    transition: background-color 0.2s;
    font-size: 18px;
}

.activity-item:hover {
    background-color: #3c3c3c;
}

.activity-item.active {
    background-color: #007acc;
    color: white;
}

/* 侧边栏 */
.sidebar {
    width: 250px;
    background-color: #252526;
    border-right: 1px solid #2d2d30;
    overflow-y: auto;
}

.panel {
    display: none;
}

.panel.active {
    display: block;
}

.panel-header {
    padding: 10px 15px;
    background-color: #2d2d30;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #3e3e42;
}

.panel-title {
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
    color: #cccccc;
}

.panel-actions {
    display: flex;
    gap: 8px;
}

.panel-actions i {
    cursor: pointer;
    padding: 2px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.panel-actions i:hover {
    background-color: #3c3c3c;
}

/* 文件树 */
.file-tree {
    padding: 5px 0;
    user-select: none;
}

/* 树形结构基础样式 */
.tree-item {
    position: relative;
}

.tree-item-content {
    display: flex;
    align-items: center;
    padding: 2px 8px;
    cursor: pointer;
    font-size: 13px;
    border-radius: 3px;
    transition: background-color 0.2s;
    min-height: 22px;
    gap: 0;
}

.tree-item-content:hover {
    background-color: #2a2d2e;
}

.tree-item.selected > .tree-item-content {
    background-color: #094771;
}

.tree-item.active > .tree-item-content {
    background-color: #37373d;
}

/* 展开/收缩图标 */
.expand-icon {
    width: 16px;
    height: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 2px;
    font-size: 10px;
    color: #cccccc;
    flex-shrink: 0;
    line-height: 1;
}

.expand-icon:hover {
    color: #ffffff;
}

/* 文件夹和文件图标 */
.folder-icon,
.file-icon {
    width: 16px;
    height: 16px;
    margin-right: 3px;
    font-size: 14px;
    flex-shrink: 0;
    display: flex;
    align-items: center;
    justify-content: center;
}

.folder-icon {
    color: #dcb67a;
}

.file-icon {
    color: #cccccc;
}

/* 特定文件类型图标颜色 */
.fa-html5 {
    color: #e34c26;
}

.fa-css3-alt {
    color: #1572b6;
}

.fa-js-square {
    color: #f7df1e;
}

.fa-markdown {
    color: #083fa1;
}

.fa-code {
    color: #cbcb41;
}

.fa-image {
    color: #4caf50;
}

/* 项目名称 */
.item-name {
    flex: 1;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 子项目容器 */
.tree-children {
    margin-left: 16px;
    border-left: 1px solid #464647;
    position: relative;
}

.tree-children::before {
    content: '';
    position: absolute;
    left: -1px;
    top: 0;
    bottom: 0;
    width: 1px;
    background: transparent;
}

/* 文件夹展开状态 */
.tree-item[data-expanded="true"] > .tree-item-content > .folder-icon::before {
    content: '\f07c'; /* folder-open */
}

.tree-item[data-expanded="false"] > .tree-item-content > .folder-icon::before {
    content: '\f07b'; /* folder */
}

/* 缩进层级 */
.tree-children .tree-children {
    margin-left: 16px;
}

/* 文件项特殊样式 */
.file-item .tree-item-content {
    padding-left: 8px; /* 重置左边距 */
    position: relative;
}

.file-item .tree-item-content::before {
    content: '';
    width: 18px; /* 展开图标宽度16px + 右边距2px */
    height: 16px;
    display: inline-block;
    flex-shrink: 0;
}

.file-item .expand-icon {
    display: none; /* 文件项不显示展开图标 */
}

/* 悬停效果 */
.tree-item-content:hover .expand-icon {
    /* 移除背景色，只保留颜色变化 */
}

/* 焦点状态 */
.tree-item-content:focus {
    outline: 1px solid #007acc;
    outline-offset: -1px;
}

/* 拖拽状态 */
.tree-item.dragging {
    opacity: 0.5;
}

.tree-item.drag-over {
    background-color: #094771;
}

/* 右键菜单 */
.context-menu {
    position: fixed;
    background-color: #2d2d30;
    border: 1px solid #464647;
    border-radius: 3px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    min-width: 150px;
    z-index: 2000;
    opacity: 0;
    visibility: hidden;
    transform: scale(0.95);
    transition: all 0.2s ease;
}

.context-menu.show {
    opacity: 1;
    visibility: visible;
    transform: scale(1);
}

.context-menu-item {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s;
    gap: 8px;
}

.context-menu-item:hover {
    background-color: #094771;
}

.context-menu-item:first-child {
    border-radius: 3px 3px 0 0;
}

.context-menu-item:last-child {
    border-radius: 0 0 3px 3px;
}

.context-menu-separator {
    height: 1px;
    background-color: #464647;
    margin: 4px 0;
}

.context-menu-item i {
    width: 16px;
    text-align: center;
}

.context-menu-item.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.context-menu-item.disabled:hover {
    background-color: transparent;
}

/* 搜索面板 */
.search-content {
    padding: 15px;
}

.search-input, .replace-input {
    width: 100%;
    padding: 8px;
    margin-bottom: 10px;
    background-color: #3c3c3c;
    border: 1px solid #464647;
    border-radius: 3px;
    color: #cccccc;
    font-size: 13px;
}

.search-input:focus, .replace-input:focus {
    outline: none;
    border-color: #007acc;
}

/* 编辑器区域 */
.editor-area {
    flex: 1;
    display: flex;
    flex-direction: column;
    background-color: #1e1e1e;
}

/* 标签栏 */
.tab-bar {
    background-color: #2d2d30;
    display: flex;
    border-bottom: 1px solid #3e3e42;
    overflow-x: auto;
}

.tab {
    display: flex;
    align-items: center;
    padding: 10px 15px;
    background-color: #2d2d30;
    border-right: 1px solid #3e3e42;
    cursor: pointer;
    font-size: 13px;
    min-width: 120px;
    transition: background-color 0.2s;
}

.tab:hover {
    background-color: #3c3c3c;
}

.tab.active {
    background-color: #1e1e1e;
    border-bottom: 2px solid #007acc;
}

.tab-name {
    flex: 1;
    margin-right: 8px;
}

.tab-close {
    font-size: 10px;
    padding: 2px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.tab-close:hover {
    background-color: #464647;
}

/* 编辑器内容 */
.editor-content {
    flex: 1;
    background-color: #1e1e1e;
    overflow: hidden;
    position: relative;
}

/* 编辑器标签页 */
.editor-tab {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: none;
    background-color: #1e1e1e;
    overflow: hidden;
}

.editor-tab.active {
    display: flex;
    z-index: 1;
}

/* 欢迎页面标签页 */
.welcome-page.editor-tab {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.welcome-page.editor-tab.active {
    display: flex;
    flex-direction: column;
}

/* 代码编辑器标签页 */
.code-editor-container.editor-tab {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
}

.code-editor-container.editor-tab.active {
    display: flex;
    flex-direction: row;
}

.line-numbers {
    background-color: #1e1e1e;
    padding: 10px 5px;
    border-right: 1px solid #3e3e42;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    color: #858585;
    user-select: none;
    min-width: 50px;
    text-align: right;
}

.line-number {
    height: 20px;
    line-height: 20px;
}

/* 欢迎页面 */
.welcome-page {
    flex: 1;
    background-color: #1e1e1e;
    overflow-y: auto;
    padding: 0;
}

.welcome-container {
    max-width: 100%;
    margin: 0;
    padding: 30px 40px;
    height: 100%;
    overflow-y: auto;
}

.welcome-header {
    text-align: center;
    margin-bottom: 30px;
    padding: 0 20px;
}

.welcome-title {
    font-size: 2.5rem;
    font-weight: 300;
    color: #ffffff;
    margin: 0 0 10px 0;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 15px;
}

.welcome-title i {
    color: #007acc;
    font-size: 2.2rem;
}

.welcome-subtitle {
    font-size: 1.1rem;
    color: #cccccc;
    margin: 0;
    font-weight: 300;
}

.welcome-content {
    display: grid;
    grid-template-columns: 1fr 1.2fr 1fr;
    gap: 50px;
    margin-bottom: 30px;
    padding: 0 20px;
}

.welcome-section h2 {
    font-size: 1.3rem;
    color: #ffffff;
    margin: 0 0 20px 0;
    font-weight: 400;
    border-bottom: 1px solid #3e3e42;
    padding-bottom: 10px;
}

/* 开始操作区域 */
.action-grid {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
}

.action-item {
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 6px;
    padding: 20px 15px;
    cursor: pointer;
    transition: all 0.2s ease;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 8px;
}

.action-item:hover {
    background-color: #2d2d30;
    border-color: #007acc;
    transform: translateY(-2px);
}

.action-item i {
    font-size: 1.8rem;
    color: #007acc;
}

/* 新建文件按钮特殊样式 */
.action-item[data-action="new-file"] {
    border: 2px solid #007acc;
    background: linear-gradient(135deg, #252526 0%, #2d2d30 100%);
}

.action-item[data-action="new-file"]:hover {
    background: linear-gradient(135deg, #2d2d30 0%, #3e3e42 100%);
    border-color: #1f9cf0;
    box-shadow: 0 4px 12px rgba(0, 122, 204, 0.3);
}

.action-item[data-action="new-file"] i {
    color: #1f9cf0;
    font-size: 2rem;
}

.action-item span {
    color: #cccccc;
    font-weight: 500;
    font-size: 0.9rem;
}

.action-item small {
    color: #858585;
    font-size: 0.75rem;
}

/* 最近文件区域 */
.recent-files {
    margin-bottom: 15px;
}

.recent-item {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    margin-bottom: 8px;
    cursor: pointer;
    transition: all 0.2s ease;
    gap: 12px;
}

.recent-item:hover {
    background-color: #2d2d30;
    border-color: #007acc;
}

.recent-item i {
    font-size: 1.2rem;
    width: 20px;
    text-align: center;
}

.recent-item .fa-html5 { color: #e34c26; }
.recent-item .fa-css3-alt { color: #1572b6; }
.recent-item .fa-js-square { color: #f7df1e; }
.recent-item .fa-markdown { color: #083fa1; }

.recent-info {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 2px;
}

.recent-name {
    color: #cccccc;
    font-weight: 500;
    font-size: 0.9rem;
}

.recent-path {
    color: #858585;
    font-size: 0.8rem;
}

.recent-time {
    color: #858585;
    font-size: 0.75rem;
    white-space: nowrap;
}

.recent-actions {
    display: flex;
    gap: 10px;
}

.recent-action-btn {
    background-color: #252526;
    border: 1px solid #3e3e42;
    color: #cccccc;
    padding: 8px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.8rem;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    gap: 6px;
}

.recent-action-btn:hover {
    background-color: #2d2d30;
    border-color: #007acc;
}

/* 代码编辑器容器 */
.code-editor-container {
    flex: 1;
    display: flex;
    background-color: #1e1e1e;
    overflow: hidden;
}

.code-editor {
    flex: 1;
    background-color: #1e1e1e;
    color: #d4d4d4;
    border: none;
    outline: none;
    padding: 10px;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 14px;
    line-height: 20px;
    resize: none;
    white-space: pre;
    overflow-wrap: normal;
    overflow-x: auto;
}

/* 帮助链接区域 */
.help-links {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.help-link {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    background-color: #252526;
    border: 1px solid #3e3e42;
    border-radius: 4px;
    color: #cccccc;
    text-decoration: none;
    transition: all 0.2s ease;
    gap: 12px;
}

.help-link:hover {
    background-color: #2d2d30;
    border-color: #007acc;
    color: #ffffff;
}

.help-link i {
    color: #007acc;
    width: 16px;
    text-align: center;
}

/* 欢迎页面底部 */
.welcome-footer {
    text-align: center;
    padding-top: 20px;
    border-top: 1px solid #3e3e42;
    color: #858585;
    font-size: 0.8rem;
}

.welcome-footer a {
    color: #007acc;
    text-decoration: none;
}

.welcome-footer a:hover {
    text-decoration: underline;
}

/* 欢迎页面响应式设计 */
@media (max-width: 1024px) {
    .welcome-content {
        grid-template-columns: 1fr 1fr;
        gap: 30px;
    }

    .welcome-container {
        padding: 30px 15px;
    }
}

@media (max-width: 768px) {
    .welcome-content {
        grid-template-columns: 1fr;
        gap: 25px;
    }

    .welcome-title {
        font-size: 2rem;
        flex-direction: column;
        gap: 10px;
    }

    .action-grid {
        grid-template-columns: 1fr;
        gap: 10px;
    }

    .action-item {
        padding: 15px 10px;
    }

    .welcome-container {
        padding: 20px 10px;
    }
}

@media (max-width: 480px) {
    .welcome-title {
        font-size: 1.5rem;
    }

    .welcome-subtitle {
        font-size: 1rem;
    }

    .recent-item {
        padding: 10px 12px;
    }

    .recent-actions {
        flex-direction: column;
    }

    .recent-action-btn {
        justify-content: center;
    }
}

/* 状态栏 */
.status-bar {
    height: 22px;
    background-color: #007acc;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 10px;
    font-size: 12px;
    color: white;
}

.status-left, .status-right {
    display: flex;
    gap: 15px;
}

.status-item {
    display: flex;
    align-items: center;
    gap: 5px;
    cursor: pointer;
    padding: 2px 5px;
    border-radius: 3px;
    transition: background-color 0.2s;
}

.status-item:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
    .sidebar {
        width: 200px;
        position: absolute;
        left: 48px;
        top: 30px;
        bottom: 22px;
        z-index: 1000;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }

    .sidebar.mobile-open {
        transform: translateX(0);
    }

    .menu-items {
        display: none;
    }

    .tab {
        min-width: 100px;
        padding: 8px 10px;
    }

    .line-numbers {
        min-width: 40px;
        padding: 10px 3px;
    }

    .code-editor {
        font-size: 12px;
    }

    .editor-area {
        margin-left: 0;
    }
}

@media (max-width: 480px) {
    .activity-bar {
        width: 40px;
    }

    .activity-item {
        width: 32px;
        height: 32px;
        font-size: 14px;
    }

    .sidebar {
        width: 100%;
        left: 0;
    }

    .panel-header {
        padding: 8px 10px;
    }

    .status-bar {
        font-size: 10px;
    }

    .status-left, .status-right {
        gap: 8px;
    }

    .tab-bar {
        overflow-x: auto;
        scrollbar-width: none;
        -ms-overflow-style: none;
    }

    .tab-bar::-webkit-scrollbar {
        display: none;
    }
}

/* 移动端侧边栏遮罩 */
.sidebar-overlay {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 999;
}

@media (max-width: 768px) {
    .sidebar-overlay.active {
        display: block;
    }
}

/* 语法高亮样式 */
.syntax-keyword {
    color: #569cd6;
}

.syntax-string {
    color: #ce9178;
}

.syntax-comment {
    color: #6a9955;
    font-style: italic;
}

.syntax-number {
    color: #b5cea8;
}

.syntax-tag {
    color: #569cd6;
}

.syntax-attribute {
    color: #9cdcfe;
}

/* 滚动条样式 */
::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

::-webkit-scrollbar-track {
    background: #1e1e1e;
}

::-webkit-scrollbar-thumb {
    background: #424242;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #4f4f4f;
}

/* 选择文本样式 */
::selection {
    background-color: #264f78;
}

/* 焦点样式 */
.code-editor:focus {
    outline: none;
}

/* 拖拽样式 */
.tab.dragging {
    opacity: 0.5;
}

.tab-bar.drag-over {
    background-color: #3c3c3c;
}
