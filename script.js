// VS Code 风格编辑器 JavaScript 功能

class VSCodeEditor {
    constructor() {
        this.currentFile = 'welcome';
        this.files = {
            'index.html': '<!DOCTYPE html>\n<html lang="zh-CN">\n<head>\n    <meta charset="UTF-8">\n    <meta name="viewport" content="width=device-width, initial-scale=1.0">\n    <title>VS Code Editor</title>\n</head>\n<body>\n    <h1>Hello World!</h1>\n</body>\n</html>',
            'styles.css': '/* CSS 样式 */\nbody {\n    font-family: Arial, sans-serif;\n    margin: 0;\n    padding: 20px;\n}\n\nh1 {\n    color: #333;\n    text-align: center;\n}',
            'script.js': '// JavaScript 代码\nconsole.log("Hello World!");\n\nfunction greet(name) {\n    return `Hello, ${name}!`;\n}\n\ngreet("VS Code Editor");',
            'README.md': '# VS Code Editor\n\n这是一个模仿 VS Code 风格的编辑器。\n\n## 功能特性\n\n- 文件管理\n- 代码编辑\n- 语法高亮\n- 标签页切换\n- 响应式设计',
            'main.css': '/* 主样式文件 */\n.container {\n    max-width: 1200px;\n    margin: 0 auto;\n    padding: 20px;\n}\n\n.header {\n    background: #333;\n    color: white;\n    padding: 1rem;\n}',
            'reset.css': '/* CSS Reset */\n* {\n    margin: 0;\n    padding: 0;\n    box-sizing: border-box;\n}\n\nbody {\n    font-family: Arial, sans-serif;\n}',
            'utils.js': '// 工具函数\nfunction formatDate(date) {\n    return date.toLocaleDateString();\n}\n\nfunction debounce(func, wait) {\n    let timeout;\n    return function executedFunction(...args) {\n        const later = () => {\n            clearTimeout(timeout);\n            func(...args);\n        };\n        clearTimeout(timeout);\n        timeout = setTimeout(later, wait);\n    };\n}',
            'config.js': '// 配置文件\nconst config = {\n    apiUrl: "https://api.example.com",\n    timeout: 5000,\n    retries: 3,\n    debug: true\n};\n\nexport default config;',
            'package.json': '{\n  "name": "vscode-editor",\n  "version": "1.0.0",\n  "description": "A VS Code style editor",\n  "main": "index.html",\n  "scripts": {\n    "start": "live-server",\n    "build": "webpack --mode production"\n  },\n  "keywords": ["editor", "vscode", "html", "css", "javascript"],\n  "author": "Developer",\n  "license": "MIT"\n}',
            'CHANGELOG.md': '# 更新日志\n\n## [1.0.0] - 2024-01-01\n\n### 新增\n- 初始版本发布\n- VS Code 风格界面\n- 基础编辑功能\n- 文件管理\n- 响应式设计\n\n### 修复\n- 修复了一些样式问题\n- 优化了性能'
        };
        this.openTabs = ['welcome']; // 默认打开欢迎标签页
        this.isMobile = window.innerWidth <= 768;
        this.sidebarOpen = false;
        this.undoStack = [];
        this.redoStack = [];
        this.recentFiles = this.loadRecentFiles();
        this.terminals = new Map(); // 存储所有终端实例
        this.activeTerminalId = null;
        this.terminalCounter = 0;
        this.terminalVisible = true;
        this.aiChatHistory = [];
        this.aiPanelVisible = true;
        this.sidebarWidth = this.loadSidebarWidth(); // 加载保存的侧边栏宽度
        this.terminalHeight = this.loadTerminalHeight(); // 加载保存的终端高度
        this.aiPanelWidth = this.loadAIPanelWidth(); // 加载保存的AI面板宽度
        this.zoomLevel = this.loadZoomLevel(); // 加载保存的缩放级别
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.updateRecentFiles();
        this.setupWelcomePage();
        this.setupTerminal();
        this.setupAIAssistant();
        this.setupSidebarSplitter();
        this.initializeZoom();

        // 确保初始状态正确：显示欢迎页面
        this.showTab(this.currentFile);
    }

    setupEventListeners() {
        // 活动栏切换
        document.querySelectorAll('.activity-item').forEach(item => {
            item.addEventListener('click', (e) => {
                this.switchPanel(e.currentTarget.dataset.panel);
                if (this.isMobile) {
                    this.toggleSidebar();
                }
            });
        });

        // 文件树点击事件
        this.setupFileTreeEvents();

        // 标签页事件（使用事件委托）
        this.setupTabEvents();

        // 代码编辑器事件将在创建编辑器标签页时动态添加

        // 新建文件
        document.querySelector('.fa-file-plus').addEventListener('click', () => {
            this.createNewFile();
        });

        // 搜索功能
        const searchInput = document.querySelector('.search-input');
        searchInput.addEventListener('input', (e) => {
            this.searchInCode(e.target.value);
        });

        // 窗口控制
        document.querySelector('.control.minimize').addEventListener('click', () => {
            console.log('最小化窗口');
        });

        document.querySelector('.control.maximize').addEventListener('click', () => {
            console.log('最大化窗口');
        });

        document.querySelector('.control.close').addEventListener('click', () => {
            console.log('关闭窗口');
        });

        // 侧边栏遮罩点击
        document.querySelector('.sidebar-overlay').addEventListener('click', () => {
            this.closeSidebar();
        });

        // 窗口大小改变
        window.addEventListener('resize', () => {
            this.isMobile = window.innerWidth <= 768;
            if (!this.isMobile) {
                this.closeSidebar();
            }
        });

        // 键盘快捷键
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // 菜单项点击
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleMenu(item);
            });
        });

        // 菜单选项点击
        document.querySelectorAll('.menu-option').forEach(option => {
            option.addEventListener('click', (e) => {
                e.stopPropagation();
                const action = option.dataset.action;
                this.handleMenuAction(action);
                this.closeAllMenus();
            });
        });

        // 点击其他地方关闭菜单
        document.addEventListener('click', () => {
            this.closeAllMenus();
            this.hideContextMenu();
            this.hideTabContextMenu();
            this.hideEditorContextMenu();
        });

        // 右键菜单事件
        this.setupContextMenu();
        this.setupEditorContextMenu();
    }

    // 设置欢迎页面
    setupWelcomePage() {
        // 欢迎页面操作按钮事件
        document.querySelectorAll('.action-item').forEach(item => {
            item.addEventListener('click', () => {
                const action = item.dataset.action;
                this.handleWelcomeAction(action);
            });
        });

        // 最近文件点击事件
        document.querySelectorAll('.recent-item').forEach(item => {
            item.addEventListener('click', () => {
                const fileName = item.dataset.file;
                if (fileName && this.files[fileName]) {
                    this.openFile(fileName);
                }
            });
        });

        // 最近文件操作按钮
        document.querySelectorAll('.recent-action-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const action = btn.dataset.action;
                this.handleRecentAction(action);
            });
        });

        // 帮助链接事件
        document.querySelectorAll('.help-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const action = link.dataset.action;
                this.handleHelpAction(action);
            });
        });

        // 底部链接事件
        document.querySelectorAll('.welcome-footer a').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const action = link.dataset.action;
                this.handleFooterAction(action);
            });
        });
    }

    // 显示指定标签页
    showTab(fileName) {
        // 强制隐藏所有标签页内容
        document.querySelectorAll('.editor-tab').forEach(tab => {
            tab.classList.remove('active');
            tab.style.display = 'none';
        });

        // 显示指定标签页
        if (fileName === 'welcome') {
            const welcomePage = document.getElementById('welcomePage');
            welcomePage.classList.add('active');
            welcomePage.style.display = 'flex';
        } else {
            const editorTab = document.querySelector(`[data-file="${fileName}"].editor-tab`);
            if (editorTab) {
                editorTab.classList.add('active');
                editorTab.style.display = 'flex';
            }
        }
    }

    // 创建代码编辑器标签页
    createEditorTab(fileName) {
        const editorContent = document.querySelector('.editor-content');
        const editorTab = document.createElement('div');
        editorTab.className = 'editor-tab code-editor-container';
        editorTab.dataset.file = fileName;

        editorTab.innerHTML = `
            <div class="line-numbers">
                <div class="line-number">1</div>
            </div>
            <textarea class="code-editor" placeholder="开始编写代码..."></textarea>
        `;

        editorContent.appendChild(editorTab);

        // 为新的编辑器添加事件监听器
        const codeEditor = editorTab.querySelector('.code-editor');
        codeEditor.addEventListener('input', () => {
            this.updateLineNumbers(editorTab);
            this.saveCurrentFile();
        });

        codeEditor.addEventListener('scroll', () => {
            this.syncLineNumbers(editorTab);
        });

        codeEditor.addEventListener('contextmenu', (e) => {
            e.preventDefault();
            this.showEditorContextMenu(e.clientX, e.clientY);
        });

        // 应用当前的缩放级别
        if (this.zoomLevel !== 1.0) {
            const fontSize = Math.round(14 * this.zoomLevel);
            codeEditor.style.fontSize = fontSize + 'px';
            const lineNumbers = editorTab.querySelector('.line-numbers');
            if (lineNumbers) {
                lineNumbers.style.fontSize = fontSize + 'px';
            }
        }

        return editorTab;
    }

    // 处理欢迎页面操作
    handleWelcomeAction(action) {
        switch (action) {
            case 'new-file':
                this.createNewFile();
                break;
            case 'open-file':
                this.openFileDialog();
                break;
            case 'open-folder':
                this.openFolderDialog();
                break;
            case 'clone-repo':
                this.cloneRepository();
                break;
            default:
                console.log(`未实现的欢迎页面操作: ${action}`);
        }
    }

    // 处理最近文件操作
    handleRecentAction(action) {
        switch (action) {
            case 'clear-recent':
                this.clearRecentFiles();
                break;
            case 'more-recent':
                this.showMoreRecentFiles();
                break;
            default:
                console.log(`未实现的最近文件操作: ${action}`);
        }
    }

    // 处理帮助操作
    handleHelpAction(action) {
        switch (action) {
            case 'show-shortcuts':
                this.showShortcuts();
                break;
            case 'show-docs':
                this.showDocumentation();
                break;
            case 'show-tips':
                this.showTipsAndTricks();
                break;
            case 'show-about':
                this.showAbout();
                break;
            default:
                console.log(`未实现的帮助操作: ${action}`);
        }
    }

    // 处理底部链接操作
    handleFooterAction(action) {
        switch (action) {
            case 'show-changelog':
                this.showChangelog();
                break;
            case 'show-settings':
                this.showSettings();
                break;
            default:
                console.log(`未实现的底部操作: ${action}`);
        }
    }

    switchPanel(panelName) {
        // 更新活动栏状态
        document.querySelectorAll('.activity-item').forEach(item => {
            item.classList.remove('active');
        });
        document.querySelector(`[data-panel="${panelName}"]`).classList.add('active');

        // 切换面板
        document.querySelectorAll('.panel').forEach(panel => {
            panel.classList.remove('active');
        });
        document.querySelector(`.${panelName}-panel`).classList.add('active');
    }

    openFile(fileName) {
        if (!this.openTabs.includes(fileName)) {
            this.openTabs.push(fileName);
            this.createTab(fileName);

            // 如果不是欢迎页面，创建编辑器标签页
            if (fileName !== 'welcome') {
                this.createEditorTab(fileName);
            }
        }
        this.switchTab(fileName);

        // 添加到最近文件列表
        if (fileName !== 'welcome') {
            this.addToRecentFiles(fileName);
        }
    }

    createTab(fileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tab = document.createElement('div');
        tab.className = 'tab';
        tab.dataset.file = fileName;
        
        const fileIcon = this.getFileIcon(fileName);
        tab.innerHTML = `
            <i class="${fileIcon}"></i>
            <span class="tab-name">${fileName}</span>
            <i class="fas fa-times tab-close"></i>
        `;

        tabBar.appendChild(tab);
    }

    switchTab(fileName) {
        // 保存当前文件
        this.saveCurrentFile();

        // 更新当前文件
        this.currentFile = fileName;

        // 更新标签页状态
        document.querySelectorAll('.tab-bar .tab').forEach(tab => {
            tab.classList.remove('active');
        });
        const activeTab = document.querySelector(`.tab-bar .tab[data-file="${fileName}"]`);
        if (activeTab) {
            activeTab.classList.add('active');
        }

        // 显示对应的标签页内容
        this.showTab(fileName);

        // 如果不是欢迎页面，高亮文件在树中的位置并加载文件内容
        if (fileName !== 'welcome') {
            this.highlightFileInTree(fileName);
            this.loadFile(fileName);
        }
    }

    closeTab(fileName) {
        // 不允许关闭欢迎标签页，如果只剩欢迎标签页
        if (fileName === 'welcome' && this.openTabs.length === 1) {
            return;
        }

        const tabIndex = this.openTabs.indexOf(fileName);
        if (tabIndex > -1) {
            this.openTabs.splice(tabIndex, 1);

            // 删除标签栏中的标签页
            const tabToRemove = document.querySelector(`.tab-bar .tab[data-file="${fileName}"]`);
            if (tabToRemove) {
                tabToRemove.remove();
            }

            // 删除编辑器标签页内容（如果不是欢迎页面）
            if (fileName !== 'welcome') {
                const editorTabToRemove = document.querySelector(`[data-file="${fileName}"].editor-tab`);
                if (editorTabToRemove) {
                    editorTabToRemove.remove();
                }
            }

            // 如果关闭的是当前标签页，切换到其他标签页
            if (fileName === this.currentFile && this.openTabs.length > 0) {
                this.switchTab(this.openTabs[this.openTabs.length - 1]);
            } else if (this.openTabs.length === 0) {
                // 如果没有标签页了，重新打开欢迎页面
                this.openTabs = ['welcome'];
                this.currentFile = 'welcome';
                this.createTab('welcome');
                this.showTab('welcome');
            }
        }
    }

    loadFile(fileName) {
        if (fileName === 'welcome') return;

        const editorTab = document.querySelector(`[data-file="${fileName}"].editor-tab`);
        if (editorTab) {
            const codeEditor = editorTab.querySelector('.code-editor');
            codeEditor.value = this.files[fileName] || '';
            this.updateLineNumbers(editorTab);
            this.updateStatusBar(fileName);
        }
    }

    saveCurrentFile() {
        if (this.currentFile && this.currentFile !== 'welcome') {
            const editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
            if (editorTab) {
                const codeEditor = editorTab.querySelector('.code-editor');
                if (codeEditor) {
                    this.files[this.currentFile] = codeEditor.value;
                }
            }
        }
    }

    updateLineNumbers(editorTab = null) {
        // 如果没有指定编辑器标签页，使用当前活动的编辑器
        if (!editorTab) {
            editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
        }

        if (!editorTab) return;

        const codeEditor = editorTab.querySelector('.code-editor');
        const lineNumbers = editorTab.querySelector('.line-numbers');

        if (!codeEditor || !lineNumbers) return;

        const lines = codeEditor.value.split('\n');

        lineNumbers.innerHTML = '';
        for (let i = 1; i <= Math.max(lines.length, 10); i++) {
            const lineNumber = document.createElement('div');
            lineNumber.className = 'line-number';
            lineNumber.textContent = i;
            lineNumbers.appendChild(lineNumber);
        }
    }

    syncLineNumbers(editorTab = null) {
        // 如果没有指定编辑器标签页，使用当前活动的编辑器
        if (!editorTab) {
            editorTab = document.querySelector(`[data-file="${this.currentFile}"].editor-tab`);
        }

        if (!editorTab) return;

        const codeEditor = editorTab.querySelector('.code-editor');
        const lineNumbers = editorTab.querySelector('.line-numbers');

        if (codeEditor && lineNumbers) {
            lineNumbers.scrollTop = codeEditor.scrollTop;
        }
    }

    updateStatusBar(fileName) {
        const fileType = this.getFileType(fileName);
        const statusRight = document.querySelector('.status-right');
        const fileTypeElement = statusRight.children[2];
        fileTypeElement.textContent = fileType;
    }

    getFileIcon(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const iconMap = {
            'html': 'fab fa-html5',
            'css': 'fab fa-css3-alt',
            'js': 'fab fa-js-square',
            'md': 'fab fa-markdown',
            'json': 'fas fa-code',
            'txt': 'fas fa-file-alt'
        };
        return iconMap[extension] || 'fas fa-file';
    }

    getFileType(fileName) {
        const extension = fileName.split('.').pop().toLowerCase();
        const typeMap = {
            'html': 'HTML',
            'css': 'CSS',
            'js': 'JavaScript',
            'md': 'Markdown',
            'json': 'JSON',
            'txt': 'Plain Text'
        };
        return typeMap[extension] || 'Unknown';
    }

    createNewFile() {
        const fileName = prompt('请输入文件名:');
        if (fileName && !this.files[fileName]) {
            this.files[fileName] = '';
            
            // 添加到文件列表
            const fileList = document.querySelector('.file-list');
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.dataset.file = fileName;
            
            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <span>${fileName}</span>
            `;
            
            fileItem.addEventListener('click', () => {
                this.openFile(fileName);
            });
            
            fileList.appendChild(fileItem);
            this.openFile(fileName);
        }
    }

    searchInCode(searchTerm) {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        if (searchTerm) {
            // 简单的搜索高亮（实际项目中可以使用更复杂的实现）
            const regex = new RegExp(searchTerm, 'gi');
            const matches = content.match(regex);
            console.log(`找到 ${matches ? matches.length : 0} 个匹配项`);
        }
    }

    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        if (this.sidebarOpen) {
            this.closeSidebar();
        } else {
            this.openSidebar();
        }
    }

    openSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.add('mobile-open');
        overlay.classList.add('active');
        this.sidebarOpen = true;
    }

    closeSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay');

        sidebar.classList.remove('mobile-open');
        overlay.classList.remove('active');
        this.sidebarOpen = false;
    }

    handleKeyboardShortcuts(e) {
        // 关闭菜单
        if (e.key === 'Escape') {
            this.closeAllMenus();
            if (this.isMobile && this.sidebarOpen) {
                this.closeSidebar();
            }
        }

        // Ctrl+S 保存
        if (e.ctrlKey && e.key === 's') {
            e.preventDefault();
            this.saveCurrentFile();
            console.log('文件已保存');
        }

        // Ctrl+N 新建文件
        if (e.ctrlKey && e.key === 'n') {
            e.preventDefault();
            this.createNewFile();
        }

        // Ctrl+O 打开文件
        if (e.ctrlKey && e.key === 'o') {
            e.preventDefault();
            this.openFileDialog();
        }

        // Ctrl+W 关闭标签页
        if (e.ctrlKey && e.key === 'w') {
            e.preventDefault();
            if (this.currentFile) {
                this.closeTab(this.currentFile);
            }
        }

        // Ctrl+F 搜索
        if (e.ctrlKey && e.key === 'f') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.search-input').focus();
        }

        // Ctrl+H 替换
        if (e.ctrlKey && e.key === 'h') {
            e.preventDefault();
            this.switchPanel('search');
            document.querySelector('.replace-input').focus();
        }

        // Ctrl+B 切换侧边栏
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebarVisibility();
        }

        // Ctrl+G 转到行
        if (e.ctrlKey && e.key === 'g') {
            e.preventDefault();
            this.goToLine();
        }

        // Ctrl+P 转到文件
        if (e.ctrlKey && e.key === 'p') {
            e.preventDefault();
            this.goToFile();
        }

        // Ctrl+Shift+` 新建终端
        if (e.ctrlKey && e.shiftKey && e.key === '`') {
            e.preventDefault();
            this.createNewTerminal();
        }

        // Ctrl+J 切换终端面板
        if (e.ctrlKey && e.key === 'j') {
            e.preventDefault();
            this.toggleTerminalPanel();
        }

        // Ctrl+Shift+A 切换AI助手面板
        if (e.ctrlKey && e.shiftKey && e.key === 'A') {
            e.preventDefault();
            this.toggleAIPanel();
        }

        // Ctrl+B 切换侧边栏显示/隐藏
        if (e.ctrlKey && e.key === 'b') {
            e.preventDefault();
            this.toggleSidebar();
        }

        // Ctrl+Shift+R 重置所有面板大小
        if (e.ctrlKey && e.shiftKey && e.key === 'R') {
            e.preventDefault();
            this.resetAllPanelSizes();
        }

        // Ctrl+L 选择行
        if (e.ctrlKey && e.key === 'l') {
            e.preventDefault();
            this.selectCurrentLine();
        }

        // 缩放快捷键
        if (e.ctrlKey && e.key === '=') {
            e.preventDefault();
            this.adjustZoom(1.1);
        }

        if (e.ctrlKey && e.key === '-') {
            e.preventDefault();
            this.adjustZoom(0.9);
        }

        if (e.ctrlKey && e.key === '0') {
            e.preventDefault();
            this.resetZoom();
        }

        // F5 运行文件
        if (e.key === 'F5') {
            e.preventDefault();
            this.runCurrentFile();
        }

        // Ctrl+/ 切换注释
        if (e.ctrlKey && e.key === '/') {
            e.preventDefault();
            this.toggleComment();
        }

        // Shift+Alt+F 格式化文档
        if (e.shiftKey && e.altKey && e.key === 'F') {
            e.preventDefault();
            this.formatDocument();
        }
    }

    // 简单的语法高亮
    applySyntaxHighlighting() {
        const codeEditor = document.querySelector('.code-editor');
        const content = codeEditor.value;

        // 这里可以实现更复杂的语法高亮逻辑
        // 目前只是一个示例
        console.log('应用语法高亮');
    }

    // 菜单控制
    toggleMenu(menuItem) {
        const isActive = menuItem.classList.contains('active');

        // 关闭所有菜单
        this.closeAllMenus();

        // 如果当前菜单未激活，则激活它
        if (!isActive) {
            menuItem.classList.add('active');
        }
    }

    closeAllMenus() {
        document.querySelectorAll('.menu-item').forEach(item => {
            item.classList.remove('active');
        });
    }

    // 处理菜单动作
    handleMenuAction(action) {
        switch (action) {
            case 'new-file':
                this.createNewFile();
                break;
            case 'open-file':
                this.openFileDialog();
                break;
            case 'save':
                this.saveCurrentFile();
                console.log('文件已保存');
                break;
            case 'save-as':
                this.saveAsFile();
                break;
            case 'close-file':
                if (this.currentFile) {
                    this.closeTab(this.currentFile);
                }
                break;
            case 'undo':
                document.execCommand('undo');
                break;
            case 'redo':
                document.execCommand('redo');
                break;
            case 'cut':
                document.execCommand('cut');
                break;
            case 'copy':
                document.execCommand('copy');
                break;
            case 'paste':
                document.execCommand('paste');
                break;
            case 'find':
                this.switchPanel('search');
                document.querySelector('.search-input').focus();
                break;
            case 'replace':
                this.switchPanel('search');
                document.querySelector('.replace-input').focus();
                break;
            case 'select-all':
                document.querySelector('.code-editor').select();
                break;
            case 'select-line':
                this.selectCurrentLine();
                break;
            case 'toggle-sidebar':
                this.toggleSidebarVisibility();
                break;
            case 'toggle-panel':
                console.log('切换面板');
                break;
            case 'zoom-in':
                this.adjustZoom(1.1);
                break;
            case 'zoom-out':
                this.adjustZoom(0.9);
                break;
            case 'reset-zoom':
                this.resetZoom();
                break;
            case 'reset-panels':
                this.resetAllPanelSizes();
                break;
            case 'go-to-line':
                this.goToLine();
                break;
            case 'go-to-file':
                this.goToFile();
                break;
            case 'run-file':
                this.runCurrentFile();
                break;
            case 'new-terminal':
                this.createNewTerminal();
                break;
            case 'about':
                this.showAbout();
                break;
            case 'shortcuts':
                this.showShortcuts();
                break;
            default:
                console.log(`未实现的动作: ${action}`);
        }
    }

    // 新增的菜单功能方法
    openFileDialog() {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.txt,.js,.html,.css,.md,.json';
        input.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                    this.files[file.name] = e.target.result;
                    this.addFileToExplorer(file.name);
                    this.openFile(file.name);
                };
                reader.readAsText(file);
            }
        };
        input.click();
    }

    saveAsFile() {
        if (this.currentFile) {
            const content = this.files[this.currentFile];
            const blob = new Blob([content], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = this.currentFile;
            a.click();
            URL.revokeObjectURL(url);
        }
    }

    selectCurrentLine() {
        const editor = document.querySelector('.code-editor');
        const start = editor.selectionStart;
        const value = editor.value;

        // 找到当前行的开始和结束
        let lineStart = value.lastIndexOf('\n', start - 1) + 1;
        let lineEnd = value.indexOf('\n', start);
        if (lineEnd === -1) lineEnd = value.length;

        editor.setSelectionRange(lineStart, lineEnd);
    }

    toggleSidebarVisibility() {
        const sidebar = document.querySelector('.sidebar');
        sidebar.style.display = sidebar.style.display === 'none' ? 'block' : 'none';
    }

    adjustZoom(factor) {
        // 计算新的缩放级别
        this.zoomLevel = Math.max(0.5, Math.min(3.0, this.zoomLevel * factor));
        const newSize = Math.round(14 * this.zoomLevel);

        // 应用到所有编辑器
        this.applyZoomToAllEditors(newSize);

        // 保存缩放级别
        this.saveZoomLevel(this.zoomLevel);

        // 更新状态栏显示
        this.updateZoomDisplay();

        console.log(`缩放级别: ${(this.zoomLevel * 100).toFixed(0)}%, 字体大小: ${newSize}px`);
    }

    applyZoomToAllEditors(fontSize) {
        // 应用到当前活动的编辑器
        const activeTab = document.querySelector('.editor-tab.active');
        if (activeTab) {
            const editor = activeTab.querySelector('.code-editor');
            const lineNumbers = activeTab.querySelector('.line-numbers');
            if (editor) editor.style.fontSize = fontSize + 'px';
            if (lineNumbers) lineNumbers.style.fontSize = fontSize + 'px';
        }

        // 应用到默认编辑器（如果存在）
        const defaultEditor = document.querySelector('.code-editor-container:not(.editor-tab) .code-editor');
        const defaultLineNumbers = document.querySelector('.code-editor-container:not(.editor-tab) .line-numbers');
        if (defaultEditor) defaultEditor.style.fontSize = fontSize + 'px';
        if (defaultLineNumbers) defaultLineNumbers.style.fontSize = fontSize + 'px';

        // 应用到所有标签页编辑器
        document.querySelectorAll('.editor-tab .code-editor').forEach(editor => {
            editor.style.fontSize = fontSize + 'px';
        });

        document.querySelectorAll('.editor-tab .line-numbers').forEach(lineNumbers => {
            lineNumbers.style.fontSize = fontSize + 'px';
        });
    }

    resetZoom() {
        // 重置缩放级别为1.0
        this.zoomLevel = 1.0;

        // 应用到所有编辑器
        this.applyZoomToAllEditors(14);

        // 保存缩放级别
        this.saveZoomLevel(this.zoomLevel);

        // 更新状态栏显示
        this.updateZoomDisplay();

        console.log('缩放级别重置为: 100%, 字体大小: 14px');
    }

    goToLine() {
        const lineNumber = prompt('转到行号:');
        if (lineNumber && !isNaN(lineNumber)) {
            const editor = document.querySelector('.code-editor');
            const lines = editor.value.split('\n');
            const targetLine = Math.min(Math.max(1, parseInt(lineNumber)), lines.length);

            // 计算目标位置
            let position = 0;
            for (let i = 0; i < targetLine - 1; i++) {
                position += lines[i].length + 1; // +1 for newline
            }

            editor.focus();
            editor.setSelectionRange(position, position);
        }
    }

    goToFile() {
        const fileName = prompt('输入文件名:');
        if (fileName && this.files[fileName]) {
            this.openFile(fileName);
        } else if (fileName) {
            alert('文件不存在');
        }
    }

    runCurrentFile() {
        if (this.currentFile && this.currentFile.endsWith('.html')) {
            const content = this.files[this.currentFile];
            const newWindow = window.open();
            newWindow.document.write(content);
        } else {
            console.log('运行当前文件');
        }
    }

    showAbout() {
        alert('VS Code 风格编辑器\n\n版本: 1.0.0\n使用 HTML + CSS + JavaScript 构建');
    }

    showShortcuts() {
        const shortcuts = `
键盘快捷键:

文件操作:
Ctrl+N - 新建文件
Ctrl+O - 打开文件
Ctrl+S - 保存文件
Ctrl+W - 关闭文件

编辑操作:
Ctrl+Z - 撤销
Ctrl+Y - 重做
Ctrl+X - 剪切
Ctrl+C - 复制
Ctrl+V - 粘贴
Ctrl+A - 全选
Ctrl+F - 查找
Ctrl+H - 替换

查看操作:
Ctrl+B - 切换侧边栏
Ctrl++ - 放大
Ctrl+- - 缩小
Ctrl+0 - 重置缩放

导航操作:
Ctrl+G - 转到行
Ctrl+P - 转到文件
        `;
        alert(shortcuts);
    }

    addFileToExplorer(fileName) {
        const fileTree = document.querySelector('.file-tree .tree-children');
        const existingFile = fileTree.querySelector(`[data-file="${fileName}"]`);

        if (!existingFile) {
            const fileItem = document.createElement('div');
            fileItem.className = 'tree-item file-item';
            fileItem.dataset.file = fileName;
            fileItem.dataset.path = `project/${fileName}`;

            const fileIcon = this.getFileIcon(fileName);
            fileItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="${fileIcon} file-icon"></i>
                    <span class="item-name">${fileName}</span>
                </div>
            `;

            fileItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTreeItem(fileItem);
                this.openFile(fileName);
            });

            fileTree.appendChild(fileItem);
        }
    }

    // 设置标签页事件
    setupTabEvents() {
        const tabBar = document.querySelector('.tab-bar');

        // 使用事件委托处理标签页点击
        tabBar.addEventListener('click', (e) => {
            const tab = e.target.closest('.tab');
            if (!tab) return;

            if (e.target.classList.contains('tab-close')) {
                // 点击关闭按钮
                e.stopPropagation();
                const fileName = tab.dataset.file;
                this.closeTab(fileName);
            } else {
                // 点击标签页其他区域
                const fileName = tab.dataset.file;
                this.switchTab(fileName);
            }
        });

        // 标签页右键菜单
        tabBar.addEventListener('contextmenu', (e) => {
            const tab = e.target.closest('.tab');
            if (tab) {
                e.preventDefault();
                this.showTabContextMenu(e.clientX, e.clientY, tab);
            }
        });

        // 设置标签页右键菜单事件
        this.setupTabContextMenu();
    }

    // 设置文件树事件
    setupFileTreeEvents() {
        // 为所有文件夹项添加展开/收缩事件
        document.querySelectorAll('.folder-item').forEach(folder => {
            const content = folder.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(folder);
            });
        });

        // 为所有文件项添加点击事件
        document.querySelectorAll('.file-item').forEach(file => {
            const content = file.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                const fileName = file.dataset.file;
                if (fileName) {
                    this.selectTreeItem(file);
                    this.openFile(fileName);
                }
            });
        });
    }

    // 切换文件夹展开/收缩状态
    toggleFolder(folderElement) {
        const isExpanded = folderElement.dataset.expanded === 'true';
        const children = folderElement.querySelector('.tree-children');
        const expandIcon = folderElement.querySelector('.expand-icon');
        const folderIcon = folderElement.querySelector('.folder-icon');

        if (isExpanded) {
            // 收缩文件夹
            folderElement.dataset.expanded = 'false';
            children.style.display = 'none';
            expandIcon.className = 'fas fa-chevron-right expand-icon';
            folderIcon.className = 'fas fa-folder folder-icon';
        } else {
            // 展开文件夹
            folderElement.dataset.expanded = 'true';
            children.style.display = 'block';
            expandIcon.className = 'fas fa-chevron-down expand-icon';
            folderIcon.className = 'fas fa-folder-open folder-icon';
        }
    }

    // 选择树项目
    selectTreeItem(item) {
        // 移除所有选中状态
        document.querySelectorAll('.tree-item').forEach(treeItem => {
            treeItem.classList.remove('selected');
        });

        // 添加选中状态到当前项
        item.classList.add('selected');
    }

    // 展开到指定路径
    expandToPath(path) {
        const pathParts = path.split('/');
        let currentPath = '';

        for (let i = 0; i < pathParts.length - 1; i++) {
            currentPath += (i > 0 ? '/' : '') + pathParts[i];
            const folder = document.querySelector(`[data-path="${currentPath}"]`);
            if (folder && folder.classList.contains('folder-item')) {
                if (folder.dataset.expanded === 'false') {
                    this.toggleFolder(folder);
                }
            }
        }
    }

    // 查找文件在树中的位置
    findFileInTree(fileName) {
        return document.querySelector(`[data-file="${fileName}"]`);
    }

    // 高亮文件在树中的位置
    highlightFileInTree(fileName) {
        const fileItem = this.findFileInTree(fileName);
        if (fileItem) {
            // 展开到该文件的路径
            const path = fileItem.dataset.path;
            this.expandToPath(path);

            // 选中该文件
            this.selectTreeItem(fileItem);

            // 滚动到可见区域
            fileItem.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }
    }

    // 获取文件夹内容
    getFolderContents(folderPath) {
        // 这里可以实现动态加载文件夹内容的逻辑
        // 目前返回静态内容
        return [];
    }

    // 创建新文件夹
    createNewFolder() {
        const folderName = prompt('请输入文件夹名称:');
        if (folderName) {
            // 这里可以实现创建新文件夹的逻辑
            console.log(`创建文件夹: ${folderName}`);
        }
    }

    // 重命名文件或文件夹
    renameItem(item) {
        const currentName = item.querySelector('.item-name').textContent;
        const newName = prompt('请输入新名称:', currentName);
        if (newName && newName !== currentName) {
            item.querySelector('.item-name').textContent = newName;
            // 这里可以实现重命名的逻辑
            console.log(`重命名: ${currentName} -> ${newName}`);
        }
    }

    // 删除文件或文件夹
    deleteItem(item) {
        const itemName = item.querySelector('.item-name').textContent;
        if (confirm(`确定要删除 "${itemName}" 吗？`)) {
            item.remove();
            // 这里可以实现删除的逻辑
            console.log(`删除: ${itemName}`);
        }
    }

    // 设置右键菜单
    setupContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        let currentContextItem = null;

        // 为文件树项添加右键事件
        document.addEventListener('contextmenu', (e) => {
            const treeItem = e.target.closest('.tree-item');
            if (treeItem && treeItem.closest('.file-tree')) {
                e.preventDefault();
                currentContextItem = treeItem;
                this.showContextMenu(e.clientX, e.clientY, treeItem);
            }
        });

        // 右键菜单项点击事件
        document.querySelectorAll('.context-menu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                const action = item.dataset.action;
                this.handleContextMenuAction(action, currentContextItem);
                this.hideContextMenu();
            });
        });

        // 点击其他地方隐藏右键菜单
        document.addEventListener('click', (e) => {
            if (!e.target.closest('.context-menu')) {
                this.hideContextMenu();
            }
        });
    }

    // 显示右键菜单
    showContextMenu(x, y, item) {
        const contextMenu = document.getElementById('contextMenu');
        const isFolder = item.classList.contains('folder-item');

        // 根据项目类型启用/禁用菜单项
        const deleteItem = contextMenu.querySelector('[data-action="delete"]');
        const renameItem = contextMenu.querySelector('[data-action="rename"]');

        // 设置菜单位置
        contextMenu.style.left = x + 'px';
        contextMenu.style.top = y + 'px';

        // 显示菜单
        contextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = contextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            contextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            contextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏右键菜单
    hideContextMenu() {
        const contextMenu = document.getElementById('contextMenu');
        contextMenu.classList.remove('show');
    }

    // 处理右键菜单动作
    handleContextMenuAction(action, item) {
        switch (action) {
            case 'new-file':
                this.createNewFileInFolder(item);
                break;
            case 'new-folder':
                this.createNewFolderInFolder(item);
                break;
            case 'rename':
                this.renameItem(item);
                break;
            case 'delete':
                this.deleteItem(item);
                break;
            case 'copy-path':
                this.copyItemPath(item);
                break;
            default:
                console.log(`未实现的右键菜单动作: ${action}`);
        }
    }

    // 在文件夹中创建新文件
    createNewFileInFolder(folderItem) {
        const fileName = prompt('请输入文件名:');
        if (fileName) {
            // 确保文件夹是展开的
            if (folderItem.classList.contains('folder-item') && folderItem.dataset.expanded === 'false') {
                this.toggleFolder(folderItem);
            }

            // 创建新文件项
            const newFileItem = document.createElement('div');
            newFileItem.className = 'tree-item file-item';
            newFileItem.dataset.file = fileName;
            newFileItem.dataset.path = `${folderItem.dataset.path}/${fileName}`;

            const fileIcon = this.getFileIcon(fileName);
            newFileItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="${fileIcon} file-icon"></i>
                    <span class="item-name">${fileName}</span>
                </div>
            `;

            // 添加点击事件
            newFileItem.addEventListener('click', (e) => {
                e.stopPropagation();
                this.selectTreeItem(newFileItem);
                this.openFile(fileName);
            });

            // 添加到文件夹中
            const children = folderItem.querySelector('.tree-children');
            if (children) {
                children.appendChild(newFileItem);
            }

            // 创建空文件内容
            this.files[fileName] = '';

            console.log(`在 ${folderItem.dataset.path} 中创建文件: ${fileName}`);
        }
    }

    // 在文件夹中创建新文件夹
    createNewFolderInFolder(parentItem) {
        const folderName = prompt('请输入文件夹名称:');
        if (folderName) {
            // 确保父文件夹是展开的
            if (parentItem.classList.contains('folder-item') && parentItem.dataset.expanded === 'false') {
                this.toggleFolder(parentItem);
            }

            // 创建新文件夹项
            const newFolderItem = document.createElement('div');
            newFolderItem.className = 'tree-item folder-item';
            newFolderItem.dataset.path = `${parentItem.dataset.path}/${folderName}`;
            newFolderItem.dataset.expanded = 'false';

            newFolderItem.innerHTML = `
                <div class="tree-item-content">
                    <i class="fas fa-chevron-right expand-icon"></i>
                    <i class="fas fa-folder folder-icon"></i>
                    <span class="item-name">${folderName}</span>
                </div>
                <div class="tree-children" style="display: none;"></div>
            `;

            // 添加展开/收缩事件
            const content = newFolderItem.querySelector('.tree-item-content');
            content.addEventListener('click', (e) => {
                e.stopPropagation();
                this.toggleFolder(newFolderItem);
            });

            // 添加到父文件夹中
            const children = parentItem.querySelector('.tree-children');
            if (children) {
                children.appendChild(newFolderItem);
            }

            console.log(`在 ${parentItem.dataset.path} 中创建文件夹: ${folderName}`);
        }
    }

    // 复制项目路径
    copyItemPath(item) {
        const path = item.dataset.path || item.dataset.file;
        if (path) {
            navigator.clipboard.writeText(path).then(() => {
                console.log(`路径已复制: ${path}`);
                // 这里可以显示一个提示消息
            }).catch(err => {
                console.error('复制路径失败:', err);
            });
        }
    }

    // 设置标签页右键菜单
    setupTabContextMenu() {
        // 标签页右键菜单项点击事件
        document.querySelectorAll('#tabContextMenu .context-menu-item').forEach(item => {
            item.addEventListener('click', () => {
                // 如果菜单项被禁用，不执行操作
                if (item.classList.contains('disabled')) {
                    return;
                }

                const action = item.dataset.action;
                this.handleTabContextMenuAction(action, this.currentContextTab);
                this.hideTabContextMenu();
            });
        });

        // 存储当前右键的标签页
        this.currentContextTab = null;
    }

    // 显示标签页右键菜单
    showTabContextMenu(x, y, tab) {
        const tabContextMenu = document.getElementById('tabContextMenu');
        this.currentContextTab = tab;

        // 获取标签页在标签栏中的位置
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const tabIndex = tabs.indexOf(tab);

        // 根据标签页位置启用/禁用菜单项
        const closeLeftItem = tabContextMenu.querySelector('[data-action="close-left"]');
        const closeRightItem = tabContextMenu.querySelector('[data-action="close-right"]');
        const closeOthersItem = tabContextMenu.querySelector('[data-action="close-others"]');

        // 如果是第一个标签页，禁用"关闭左边标签"
        if (tabIndex === 0) {
            closeLeftItem.classList.add('disabled');
        } else {
            closeLeftItem.classList.remove('disabled');
        }

        // 如果是最后一个标签页，禁用"关闭右边标签"
        if (tabIndex === tabs.length - 1) {
            closeRightItem.classList.add('disabled');
        } else {
            closeRightItem.classList.remove('disabled');
        }

        // 如果只有一个标签页，禁用"关闭其他标签"
        if (tabs.length === 1) {
            closeOthersItem.classList.add('disabled');
        } else {
            closeOthersItem.classList.remove('disabled');
        }

        // 设置菜单位置
        tabContextMenu.style.left = x + 'px';
        tabContextMenu.style.top = y + 'px';

        // 显示菜单
        tabContextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = tabContextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            tabContextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            tabContextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏标签页右键菜单
    hideTabContextMenu() {
        const tabContextMenu = document.getElementById('tabContextMenu');
        tabContextMenu.classList.remove('show');
        this.currentContextTab = null;
    }

    // 处理标签页右键菜单动作
    handleTabContextMenuAction(action, tab) {
        if (!tab) return;

        const fileName = tab.dataset.file;

        switch (action) {
            case 'close-current':
                this.closeTab(fileName);
                break;
            case 'close-others':
                this.closeOtherTabs(fileName);
                break;
            case 'close-left':
                this.closeLeftTabs(fileName);
                break;
            case 'close-right':
                this.closeRightTabs(fileName);
                break;
            case 'close-all':
                this.closeAllTabs();
                break;
            case 'copy-file-path':
                this.copyFilePath(fileName);
                break;
            case 'reveal-in-explorer':
                this.revealInExplorer(fileName);
                break;
            default:
                console.log(`未实现的标签页菜单动作: ${action}`);
        }
    }

    // 关闭其他标签页
    closeOtherTabs(keepFileName) {
        const tabsToClose = [...this.openTabs];
        tabsToClose.forEach(fileName => {
            if (fileName !== keepFileName) {
                this.closeTab(fileName);
            }
        });
    }

    // 关闭左边的标签页
    closeLeftTabs(targetFileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const targetTab = tabs.find(tab => tab.dataset.file === targetFileName);
        const targetIndex = tabs.indexOf(targetTab);

        // 关闭目标标签页左边的所有标签页
        for (let i = 0; i < targetIndex; i++) {
            const fileName = tabs[i].dataset.file;
            this.closeTab(fileName);
        }
    }

    // 关闭右边的标签页
    closeRightTabs(targetFileName) {
        const tabBar = document.querySelector('.tab-bar');
        const tabs = Array.from(tabBar.querySelectorAll('.tab'));
        const targetTab = tabs.find(tab => tab.dataset.file === targetFileName);
        const targetIndex = tabs.indexOf(targetTab);

        // 关闭目标标签页右边的所有标签页
        for (let i = targetIndex + 1; i < tabs.length; i++) {
            const fileName = tabs[i].dataset.file;
            this.closeTab(fileName);
        }
    }

    // 关闭所有标签页
    closeAllTabs() {
        const tabsToClose = [...this.openTabs];
        tabsToClose.forEach(fileName => {
            this.closeTab(fileName);
        });
    }

    // 复制文件路径
    copyFilePath(fileName) {
        const path = `project/${fileName}`;
        navigator.clipboard.writeText(path).then(() => {
            console.log(`文件路径已复制: ${path}`);
            // 这里可以显示一个提示消息
        }).catch(err => {
            console.error('复制文件路径失败:', err);
        });
    }

    // 在资源管理器中显示
    revealInExplorer(fileName) {
        // 高亮文件在文件树中的位置
        this.highlightFileInTree(fileName);

        // 确保资源管理器面板是打开的
        this.switchPanel('explorer');

        console.log(`在资源管理器中显示: ${fileName}`);
    }

    // 设置代码编辑器右键菜单
    setupEditorContextMenu() {
        // 代码编辑器右键菜单项点击事件
        document.querySelectorAll('#editorContextMenu .context-menu-item').forEach(item => {
            item.addEventListener('click', () => {
                // 如果菜单项被禁用，不执行操作
                if (item.classList.contains('disabled')) {
                    return;
                }

                const action = item.dataset.action;
                this.handleEditorContextMenuAction(action);
                this.hideEditorContextMenu();
            });
        });
    }

    // 显示代码编辑器右键菜单
    showEditorContextMenu(x, y) {
        const editorContextMenu = document.getElementById('editorContextMenu');
        const codeEditor = document.querySelector('.code-editor');

        // 检查编辑器状态，启用/禁用相应菜单项
        const hasSelection = codeEditor.selectionStart !== codeEditor.selectionEnd;
        const hasContent = codeEditor.value.length > 0;

        // 根据状态启用/禁用菜单项
        const cutItem = editorContextMenu.querySelector('[data-action="cut"]');
        const copyItem = editorContextMenu.querySelector('[data-action="copy"]');
        const selectAllItem = editorContextMenu.querySelector('[data-action="select-all"]');

        // 如果没有选中文本，禁用剪切和复制
        if (hasSelection) {
            cutItem.classList.remove('disabled');
            copyItem.classList.remove('disabled');
        } else {
            cutItem.classList.add('disabled');
            copyItem.classList.add('disabled');
        }

        // 如果没有内容，禁用全选
        if (hasContent) {
            selectAllItem.classList.remove('disabled');
        } else {
            selectAllItem.classList.add('disabled');
        }

        // 设置菜单位置
        editorContextMenu.style.left = x + 'px';
        editorContextMenu.style.top = y + 'px';

        // 显示菜单
        editorContextMenu.classList.add('show');

        // 确保菜单在视窗内
        const rect = editorContextMenu.getBoundingClientRect();
        if (rect.right > window.innerWidth) {
            editorContextMenu.style.left = (x - rect.width) + 'px';
        }
        if (rect.bottom > window.innerHeight) {
            editorContextMenu.style.top = (y - rect.height) + 'px';
        }
    }

    // 隐藏代码编辑器右键菜单
    hideEditorContextMenu() {
        const editorContextMenu = document.getElementById('editorContextMenu');
        editorContextMenu.classList.remove('show');
    }

    // 处理代码编辑器右键菜单动作
    handleEditorContextMenuAction(action) {
        const codeEditor = document.querySelector('.code-editor');

        switch (action) {
            case 'undo':
                this.editorUndo();
                break;
            case 'redo':
                this.editorRedo();
                break;
            case 'cut':
                this.editorCut();
                break;
            case 'copy':
                this.editorCopy();
                break;
            case 'paste':
                this.editorPaste();
                break;
            case 'select-all':
                codeEditor.select();
                break;
            case 'select-line':
                this.selectCurrentLine();
                break;
            case 'find':
                this.switchPanel('search');
                document.querySelector('.search-input').focus();
                break;
            case 'replace':
                this.switchPanel('search');
                document.querySelector('.replace-input').focus();
                break;
            case 'go-to-line':
                this.goToLine();
                break;
            case 'toggle-comment':
                this.toggleComment();
                break;
            case 'format-document':
                this.formatDocument();
                break;
            default:
                console.log(`未实现的编辑器菜单动作: ${action}`);
        }
    }

    // 编辑器撤销功能
    editorUndo() {
        const codeEditor = document.querySelector('.code-editor');
        if (this.undoStack && this.undoStack.length > 0) {
            const previousState = this.undoStack.pop();
            this.redoStack = this.redoStack || [];
            this.redoStack.push(codeEditor.value);
            codeEditor.value = previousState;
            this.updateLineNumbers();
        }
    }

    // 编辑器重做功能
    editorRedo() {
        const codeEditor = document.querySelector('.code-editor');
        if (this.redoStack && this.redoStack.length > 0) {
            const nextState = this.redoStack.pop();
            this.undoStack = this.undoStack || [];
            this.undoStack.push(codeEditor.value);
            codeEditor.value = nextState;
            this.updateLineNumbers();
        }
    }

    // 编辑器剪切功能
    editorCut() {
        const codeEditor = document.querySelector('.code-editor');
        const selectedText = codeEditor.value.substring(codeEditor.selectionStart, codeEditor.selectionEnd);

        if (selectedText) {
            // 复制到剪贴板
            navigator.clipboard.writeText(selectedText).then(() => {
                // 删除选中的文本
                const start = codeEditor.selectionStart;
                const end = codeEditor.selectionEnd;
                const newValue = codeEditor.value.substring(0, start) + codeEditor.value.substring(end);
                codeEditor.value = newValue;
                codeEditor.setSelectionRange(start, start);
                this.updateLineNumbers();
                this.saveCurrentFile();
            });
        }
    }

    // 编辑器复制功能
    editorCopy() {
        const codeEditor = document.querySelector('.code-editor');
        const selectedText = codeEditor.value.substring(codeEditor.selectionStart, codeEditor.selectionEnd);

        if (selectedText) {
            navigator.clipboard.writeText(selectedText).then(() => {
                console.log('文本已复制到剪贴板');
            });
        }
    }

    // 编辑器粘贴功能
    editorPaste() {
        const codeEditor = document.querySelector('.code-editor');

        navigator.clipboard.readText().then(text => {
            const start = codeEditor.selectionStart;
            const end = codeEditor.selectionEnd;
            const newValue = codeEditor.value.substring(0, start) + text + codeEditor.value.substring(end);
            codeEditor.value = newValue;
            codeEditor.setSelectionRange(start + text.length, start + text.length);
            this.updateLineNumbers();
            this.saveCurrentFile();
        }).catch(err => {
            console.error('粘贴失败:', err);
        });
    }

    // 切换注释
    toggleComment() {
        const codeEditor = document.querySelector('.code-editor');
        const start = codeEditor.selectionStart;
        const end = codeEditor.selectionEnd;
        const text = codeEditor.value;

        // 找到选中文本的行
        const beforeText = text.substring(0, start);
        const selectedText = text.substring(start, end);
        const afterText = text.substring(end);

        const lines = selectedText.split('\n');
        const commentedLines = lines.map(line => {
            if (line.trim().startsWith('//')) {
                // 取消注释
                return line.replace(/^\s*\/\/\s?/, '');
            } else if (line.trim().length > 0) {
                // 添加注释
                return '// ' + line;
            }
            return line;
        });

        const newSelectedText = commentedLines.join('\n');
        const newValue = beforeText + newSelectedText + afterText;

        codeEditor.value = newValue;
        codeEditor.setSelectionRange(start, start + newSelectedText.length);
        this.updateLineNumbers();
        this.saveCurrentFile();
    }

    // 格式化文档
    formatDocument() {
        const codeEditor = document.querySelector('.code-editor');
        let content = codeEditor.value;

        // 简单的格式化逻辑（可以根据文件类型扩展）
        if (this.currentFile && this.currentFile.endsWith('.html')) {
            content = this.formatHTML(content);
        } else if (this.currentFile && this.currentFile.endsWith('.css')) {
            content = this.formatCSS(content);
        } else if (this.currentFile && this.currentFile.endsWith('.js')) {
            content = this.formatJavaScript(content);
        }

        codeEditor.value = content;
        this.updateLineNumbers();
        this.saveCurrentFile();
    }

    // 简单的HTML格式化
    formatHTML(html) {
        // 这里可以实现更复杂的HTML格式化逻辑
        return html.replace(/></g, '>\n<').replace(/^\s+|\s+$/gm, '');
    }

    // 简单的CSS格式化
    formatCSS(css) {
        // 这里可以实现更复杂的CSS格式化逻辑
        return css.replace(/\{/g, ' {\n    ').replace(/\}/g, '\n}\n').replace(/;/g, ';\n    ');
    }

    // 简单的JavaScript格式化
    formatJavaScript(js) {
        // 这里可以实现更复杂的JavaScript格式化逻辑
        return js.replace(/\{/g, ' {\n    ').replace(/\}/g, '\n}\n');
    }

    // 最近文件管理
    loadRecentFiles() {
        const stored = localStorage.getItem('vscode-editor-recent-files');
        return stored ? JSON.parse(stored) : [];
    }

    saveRecentFiles() {
        localStorage.setItem('vscode-editor-recent-files', JSON.stringify(this.recentFiles));
    }

    addToRecentFiles(fileName) {
        // 移除已存在的项目
        this.recentFiles = this.recentFiles.filter(item => item.name !== fileName);

        // 添加到开头
        this.recentFiles.unshift({
            name: fileName,
            path: `project/${fileName}`,
            timestamp: Date.now()
        });

        // 限制最大数量
        if (this.recentFiles.length > 10) {
            this.recentFiles = this.recentFiles.slice(0, 10);
        }

        this.saveRecentFiles();
        this.updateRecentFiles();
    }

    updateRecentFiles() {
        const recentFilesContainer = document.getElementById('recentFiles');
        if (!recentFilesContainer) return;

        recentFilesContainer.innerHTML = '';

        if (this.recentFiles.length === 0) {
            recentFilesContainer.innerHTML = '<p style="color: #858585; text-align: center; padding: 20px;">暂无最近文件</p>';
            return;
        }

        this.recentFiles.forEach(file => {
            const recentItem = document.createElement('div');
            recentItem.className = 'recent-item';
            recentItem.dataset.file = file.name;

            const fileIcon = this.getFileIcon(file.name);
            const timeAgo = this.getTimeAgo(file.timestamp);

            recentItem.innerHTML = `
                <i class="${fileIcon}"></i>
                <div class="recent-info">
                    <span class="recent-name">${file.name}</span>
                    <span class="recent-path">${file.path}</span>
                </div>
                <span class="recent-time">${timeAgo}</span>
            `;

            recentItem.addEventListener('click', () => {
                if (this.files[file.name]) {
                    this.openFile(file.name);
                }
            });

            recentFilesContainer.appendChild(recentItem);
        });
    }

    getTimeAgo(timestamp) {
        const now = Date.now();
        const diff = now - timestamp;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        return `${days}天前`;
    }

    clearRecentFiles() {
        if (confirm('确定要清除所有最近文件记录吗？')) {
            this.recentFiles = [];
            this.saveRecentFiles();
            this.updateRecentFiles();
        }
    }

    showMoreRecentFiles() {
        alert('显示更多最近文件功能待实现');
    }

    // 欢迎页面其他功能
    openFolderDialog() {
        alert('打开文件夹功能待实现');
    }

    cloneRepository() {
        const repoUrl = prompt('请输入Git仓库URL:');
        if (repoUrl) {
            alert(`克隆仓库功能待实现: ${repoUrl}`);
        }
    }

    showDocumentation() {
        window.open('https://code.visualstudio.com/docs', '_blank');
    }

    showTipsAndTricks() {
        alert('提示和技巧:\n\n1. 使用 Ctrl+P 快速打开文件\n2. 使用 Ctrl+Shift+P 打开命令面板\n3. 使用 Ctrl+/ 切换注释\n4. 使用 Alt+↑/↓ 移动行\n5. 使用 Shift+Alt+↑/↓ 复制行');
    }

    showChangelog() {
        alert('更新日志:\n\nv1.0.0\n- 初始版本发布\n- VS Code风格界面\n- 基础编辑功能\n- 文件管理\n- 响应式设计');
    }

    showSettings() {
        alert('设置功能待实现');
    }

    // 终端功能
    setupTerminal() {
        const toggleTerminal = document.getElementById('toggleTerminal');
        const newTerminal = document.getElementById('newTerminal');
        const clearTerminal = document.getElementById('clearTerminal');
        const terminalExpandBtn = document.getElementById('terminalExpandBtn');

        // 切换终端显示/隐藏
        toggleTerminal.addEventListener('click', () => {
            this.toggleTerminalPanel();
        });

        // 新建终端
        newTerminal.addEventListener('click', () => {
            this.createNewTerminal();
        });

        // 清空当前终端
        clearTerminal.addEventListener('click', () => {
            this.clearCurrentTerminal();
        });

        // 终端展开按钮
        terminalExpandBtn.addEventListener('click', () => {
            this.toggleTerminalPanel();
        });

        // 分割条拖拽功能
        this.setupTerminalSplitter();

        // 创建第一个终端
        this.createNewTerminal();
    }

    // 创建新终端
    createNewTerminal() {
        this.terminalCounter++;
        const terminalId = `terminal-${this.terminalCounter}`;

        // 创建终端数据
        const terminal = {
            id: terminalId,
            name: `终端 ${this.terminalCounter}`,
            history: [],
            historyIndex: -1,
            output: []
        };

        this.terminals.set(terminalId, terminal);

        // 创建终端标签
        this.createTerminalTab(terminal);

        // 创建终端内容
        this.createTerminalContent(terminal);

        // 切换到新终端
        this.switchToTerminal(terminalId);

        return terminalId;
    }

    // 创建终端标签
    createTerminalTab(terminal) {
        const tabsContainer = document.getElementById('terminalTabs');
        const tab = document.createElement('div');
        tab.className = 'terminal-tab';
        tab.dataset.terminalId = terminal.id;

        tab.innerHTML = `
            <i class="fas fa-terminal"></i>
            <span>${terminal.name}</span>
            <i class="fas fa-times terminal-tab-close"></i>
        `;

        // 标签点击事件
        tab.addEventListener('click', (e) => {
            if (!e.target.classList.contains('terminal-tab-close')) {
                this.switchToTerminal(terminal.id);
            }
        });

        // 关闭按钮事件
        const closeBtn = tab.querySelector('.terminal-tab-close');
        closeBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            this.closeTerminal(terminal.id);
        });

        tabsContainer.appendChild(tab);
    }

    // 创建终端内容
    createTerminalContent(terminal) {
        const container = document.getElementById('terminalContentContainer');
        const content = document.createElement('div');
        content.className = 'terminal-content';
        content.dataset.terminalId = terminal.id;

        content.innerHTML = `
            <div class="terminal-output">
                <div class="terminal-line">Microsoft Windows [版本 10.0.19045.3570]</div>
                <div class="terminal-line">(c) Microsoft Corporation. 保留所有权利。</div>
                <div class="terminal-line"></div>
                <div class="terminal-line current-input">C:\\workspace\\vscode-editor><span class="terminal-cursor">|</span></div>
            </div>
        `;

        // 添加点击事件，点击终端区域时聚焦到输入
        content.addEventListener('click', () => {
            this.focusTerminalInput(terminal.id);
        });

        // 添加键盘事件监听
        content.addEventListener('keydown', (e) => {
            this.handleTerminalKeydown(e, terminal.id);
        });

        content.tabIndex = 0; // 使div可以接收键盘事件
        container.appendChild(content);
    }

    // 切换到指定终端
    switchToTerminal(terminalId) {
        // 更新活动终端
        this.activeTerminalId = terminalId;

        // 更新标签状态
        document.querySelectorAll('.terminal-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-terminal-id="${terminalId}"]`).classList.add('active');

        // 更新内容显示
        document.querySelectorAll('.terminal-content').forEach(content => {
            content.classList.remove('active');
        });
        document.querySelector(`[data-terminal-id="${terminalId}"]`).classList.add('active');

        // 聚焦到终端
        this.focusTerminalInput(terminalId);
    }

    // 关闭终端
    closeTerminal(terminalId) {
        if (this.terminals.size <= 1) {
            // 至少保留一个终端
            return;
        }

        // 移除终端数据
        this.terminals.delete(terminalId);

        // 移除DOM元素
        const tab = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        const content = document.querySelector(`.terminal-content[data-terminal-id="${terminalId}"]`);

        if (tab) tab.remove();
        if (content) content.remove();

        // 如果关闭的是当前活动终端，切换到第一个可用终端
        if (this.activeTerminalId === terminalId) {
            const firstTerminalId = this.terminals.keys().next().value;
            if (firstTerminalId) {
                this.switchToTerminal(firstTerminalId);
            }
        }
    }

    // 处理终端键盘输入
    handleTerminalKeydown(e, terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;

        const output = document.querySelector(`[data-terminal-id="${terminalId}"] .terminal-output`);

        if (e.key === 'Enter') {
            e.preventDefault();
            const command = terminal.currentInput || '';
            this.executeCommand(command, terminalId);
            terminal.currentInput = '';
            this.updateTerminalPrompt(terminalId);
        } else if (e.key === 'Backspace') {
            e.preventDefault();
            if (terminal.currentInput && terminal.currentInput.length > 0) {
                terminal.currentInput = terminal.currentInput.slice(0, -1);
                this.updateTerminalPrompt(terminalId);
            }
        } else if (e.key === 'ArrowUp') {
            e.preventDefault();
            this.navigateHistory(terminalId, -1);
        } else if (e.key === 'ArrowDown') {
            e.preventDefault();
            this.navigateHistory(terminalId, 1);
        } else if (e.key.length === 1) {
            // 普通字符输入
            e.preventDefault();
            terminal.currentInput = (terminal.currentInput || '') + e.key;
            this.updateTerminalPrompt(terminalId);
        }
    }

    // 更新终端提示符显示
    updateTerminalPrompt(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;

        const output = document.querySelector(`[data-terminal-id="${terminalId}"] .terminal-output`);
        const lastLine = output.lastElementChild;

        if (lastLine && lastLine.classList.contains('current-input')) {
            lastLine.innerHTML = `C:\\workspace\\vscode-editor>${terminal.currentInput || ''}<span class="terminal-cursor">|</span>`;
        }
    }

    // 聚焦终端输入
    focusTerminalInput(terminalId) {
        const content = document.querySelector(`[data-terminal-id="${terminalId}"]`);
        if (content) {
            content.focus();
        }
    }

    // 执行命令
    executeCommand(command, terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;

        const output = document.querySelector(`[data-terminal-id="${terminalId}"] .terminal-output`);

        // 移除当前输入行的光标
        const currentInputLine = output.querySelector('.current-input');
        if (currentInputLine) {
            currentInputLine.classList.remove('current-input');
            currentInputLine.innerHTML = `C:\\workspace\\vscode-editor>${command}`;
        }

        // 添加到历史记录
        if (command.trim()) {
            terminal.history.push(command);
            terminal.historyIndex = terminal.history.length;
        }

        // 执行命令并显示结果
        const result = this.processCommand(command, terminalId);
        if (result) {
            const resultLine = document.createElement('div');
            resultLine.className = 'terminal-line';
            resultLine.textContent = result;
            output.appendChild(resultLine);
        }

        // 添加新的输入行
        this.addNewInputLine(terminalId);

        // 滚动到底部
        output.scrollTop = output.scrollHeight;
    }

    // 添加新的输入行
    addNewInputLine(terminalId) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;

        const output = document.querySelector(`[data-terminal-id="${terminalId}"] .terminal-output`);
        const inputLine = document.createElement('div');
        inputLine.className = 'terminal-line current-input';
        inputLine.innerHTML = `C:\\workspace\\vscode-editor><span class="terminal-cursor">|</span>`;
        output.appendChild(inputLine);

        terminal.currentInput = '';
    }

    // 处理命令
    processCommand(command, terminalId) {
        const args = command.trim().split(' ');
        const cmd = args[0].toLowerCase();

        switch (cmd) {
            case 'help':
                return '可用命令:\n  help - 显示帮助\n  cls - 清空终端\n  dir - 列出文件\n  cd - 显示当前路径\n  echo - 输出文本\n  date - 显示当前时间\n  time - 显示当前时间\n  ver - 显示版本信息';

            case 'cls':
            case 'clear':
                this.clearTerminal(terminalId);
                return '';

            case 'dir':
            case 'ls':
                return Object.keys(this.files).join('\n');

            case 'cd':
                return 'C:\\workspace\\vscode-editor';

            case 'echo':
                return args.slice(1).join(' ');

            case 'date':
                return new Date().toLocaleDateString();

            case 'time':
                return new Date().toLocaleTimeString();

            case 'ver':
                return 'YS Code Editor v1.0.0\nMicrosoft Windows [版本 10.0.19045.3570]';

            case '':
                return '';

            default:
                return `'${cmd}' 不是内部或外部命令，也不是可运行的程序或批处理文件。`;
        }
    }

    // 历史记录导航
    navigateHistory(terminalId, direction) {
        const terminal = this.terminals.get(terminalId);
        if (!terminal) return;

        if (direction === -1 && terminal.historyIndex > 0) {
            terminal.historyIndex--;
            terminal.currentInput = terminal.history[terminal.historyIndex];
        } else if (direction === 1 && terminal.historyIndex < terminal.history.length - 1) {
            terminal.historyIndex++;
            terminal.currentInput = terminal.history[terminal.historyIndex];
        } else if (direction === 1 && terminal.historyIndex === terminal.history.length - 1) {
            terminal.historyIndex = terminal.history.length;
            terminal.currentInput = '';
        }

        this.updateTerminalPrompt(terminalId);
    }

    // 切换终端面板显示/隐藏
    toggleTerminalPanel() {
        const terminalPanel = document.getElementById('terminalPanel');
        const toggleButton = document.getElementById('toggleTerminal');
        const expandButton = document.getElementById('terminalExpandBtn');
        const icon = toggleButton.querySelector('i');

        if (this.terminalVisible) {
            terminalPanel.classList.add('hidden');
            icon.className = 'fas fa-chevron-up';
            toggleButton.title = '显示终端';
            expandButton.style.display = 'flex';
            this.terminalVisible = false;
        } else {
            terminalPanel.classList.remove('hidden');
            icon.className = 'fas fa-chevron-down';
            toggleButton.title = '隐藏终端';
            expandButton.style.display = 'none';
            this.terminalVisible = true;

            // 聚焦到当前活动终端
            if (this.activeTerminalId) {
                this.focusTerminalInput(this.activeTerminalId);
            }
        }
    }

    // 清空当前终端
    clearCurrentTerminal() {
        if (!this.activeTerminalId) return;
        this.clearTerminal(this.activeTerminalId);
    }

    // 清空指定终端
    clearTerminal(terminalId) {
        const output = document.querySelector(`[data-terminal-id="${terminalId}"] .terminal-output`);
        if (output) {
            output.innerHTML = `
                <div class="terminal-line">Microsoft Windows [版本 10.0.19045.3570]</div>
                <div class="terminal-line">(c) Microsoft Corporation. 保留所有权利。</div>
                <div class="terminal-line"></div>
            `;
            this.addNewInputLine(terminalId);
        }
    }

    setupTerminalSplitter() {
        const splitter = document.getElementById('terminalSplitter');
        const terminalPanel = document.getElementById('terminalPanel');
        const editorMain = document.querySelector('.editor-main');
        let isDragging = false;

        splitter.addEventListener('mousedown', (e) => {
            isDragging = true;
            document.body.style.cursor = 'row-resize';
            document.body.style.userSelect = 'none';
            splitter.classList.add('dragging');
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const editorArea = document.querySelector('.editor-area');
            const rect = editorArea.getBoundingClientRect();
            const newHeight = rect.bottom - e.clientY;

            // 限制终端面板的最小和最大高度
            const minHeight = 100;
            const maxHeight = rect.height * 0.7;

            if (newHeight >= minHeight && newHeight <= maxHeight) {
                terminalPanel.style.height = newHeight + 'px';
                this.terminalHeight = newHeight;
                this.saveTerminalHeight(newHeight);
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
                splitter.classList.remove('dragging');
            }
        });

        // 双击分割条重置终端高度
        splitter.addEventListener('dblclick', () => {
            const defaultHeight = 200;
            terminalPanel.style.height = defaultHeight + 'px';
            this.terminalHeight = defaultHeight;
            this.saveTerminalHeight(defaultHeight);
        });

        // 初始化终端高度
        terminalPanel.style.height = this.terminalHeight + 'px';
    }

    // AI代码助手功能
    setupAIAssistant() {
        const aiInput = document.getElementById('aiInput');
        const aiSendBtn = document.getElementById('aiSendBtn');
        const aiAttachBtn = document.getElementById('aiAttachBtn');
        const toggleAI = document.getElementById('toggleAI');
        const refreshAI = document.getElementById('refreshAI');
        const clearAI = document.getElementById('clearAI');
        const aiExpandBtn = document.getElementById('aiExpandBtn');

        // AI输入事件
        aiInput.addEventListener('keydown', (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                e.preventDefault();
                this.sendAIMessage();
            }
        });

        aiInput.addEventListener('input', () => {
            const hasContent = aiInput.value.trim().length > 0;
            aiSendBtn.disabled = !hasContent;
        });

        // 发送按钮
        aiSendBtn.addEventListener('click', () => {
            this.sendAIMessage();
        });

        // 附加文件按钮
        aiAttachBtn.addEventListener('click', () => {
            this.attachCurrentFile();
        });

        // 快捷操作按钮
        document.querySelectorAll('.ai-action-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.currentTarget.dataset.action;
                this.handleAIAction(action);
            });
        });

        // AI面板控制按钮
        toggleAI.addEventListener('click', () => {
            this.toggleAIPanel();
        });

        refreshAI.addEventListener('click', () => {
            this.refreshAIChat();
        });

        clearAI.addEventListener('click', () => {
            this.clearAIChat();
        });

        // AI展开按钮
        aiExpandBtn.addEventListener('click', () => {
            this.toggleAIPanel();
        });

        // 初始化发送按钮状态
        aiSendBtn.disabled = true;

        // 设置垂直分割条
        this.setupVerticalSplitter();
    }

    sendAIMessage() {
        const aiInput = document.getElementById('aiInput');
        const message = aiInput.value.trim();

        if (!message) return;

        // 添加用户消息
        this.addAIMessage(message, 'user');

        // 清空输入框
        aiInput.value = '';
        document.getElementById('aiSendBtn').disabled = true;

        // 显示AI正在思考
        this.showAIThinking();

        // 模拟AI响应（实际项目中这里会调用AI API）
        setTimeout(() => {
            this.generateAIResponse(message);
        }, 1000 + Math.random() * 2000);
    }

    addAIMessage(content, type = 'system') {
        const messagesContainer = document.getElementById('aiChatMessages');
        const messageDiv = document.createElement('div');
        messageDiv.className = `ai-message ai-${type}`;

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'ai-avatar';
        avatarDiv.innerHTML = type === 'user' ? '<i class="fas fa-user"></i>' : '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'ai-content';

        if (typeof content === 'string') {
            contentDiv.innerHTML = this.formatAIMessage(content);
        } else {
            contentDiv.appendChild(content);
        }

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);
        messagesContainer.appendChild(messageDiv);

        // 滚动到底部
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        // 添加到历史记录
        this.aiChatHistory.push({ type, content, timestamp: Date.now() });
    }

    formatAIMessage(message) {
        // 简单的markdown格式化
        return message
            .replace(/```([\s\S]*?)```/g, '<pre><code>$1</code></pre>')
            .replace(/`([^`]+)`/g, '<code>$1</code>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n/g, '<br>');
    }

    showAIThinking() {
        const thinkingDiv = document.createElement('div');
        thinkingDiv.className = 'ai-loading';
        thinkingDiv.textContent = 'AI正在思考...';
        thinkingDiv.id = 'ai-thinking';

        const messageDiv = document.createElement('div');
        messageDiv.className = 'ai-message ai-system';

        const avatarDiv = document.createElement('div');
        avatarDiv.className = 'ai-avatar';
        avatarDiv.innerHTML = '<i class="fas fa-robot"></i>';

        const contentDiv = document.createElement('div');
        contentDiv.className = 'ai-content';
        contentDiv.appendChild(thinkingDiv);

        messageDiv.appendChild(avatarDiv);
        messageDiv.appendChild(contentDiv);

        const messagesContainer = document.getElementById('aiChatMessages');
        messagesContainer.appendChild(messageDiv);
        messagesContainer.scrollTop = messagesContainer.scrollHeight;

        return messageDiv;
    }

    generateAIResponse(userMessage) {
        // 移除思考状态
        const thinkingElement = document.getElementById('ai-thinking');
        if (thinkingElement) {
            thinkingElement.closest('.ai-message').remove();
        }

        // 生成AI响应（这里是模拟响应，实际项目中会调用真实的AI API）
        let response = this.getAIResponse(userMessage);

        this.addAIMessage(response, 'system');
    }

    getAIResponse(message) {
        const lowerMessage = message.toLowerCase();

        if (lowerMessage.includes('解释') || lowerMessage.includes('什么') || lowerMessage.includes('如何')) {
            return `我来帮您解释一下：

**${message}**

这是一个很好的问题！根据您的询问，我建议：

1. **首先理解基本概念** - 确保掌握相关的基础知识
2. **查看代码结构** - 分析代码的组织方式和逻辑流程
3. **实践练习** - 通过实际编写代码来加深理解

如果您有具体的代码需要分析，请粘贴代码，我可以提供更详细的解释。`;
        }

        if (lowerMessage.includes('错误') || lowerMessage.includes('bug') || lowerMessage.includes('调试')) {
            return `让我帮您调试代码：

**常见错误类型：**
- **语法错误** - 检查括号、分号、引号是否匹配
- **逻辑错误** - 验证算法和条件判断
- **运行时错误** - 检查变量是否已定义，函数是否存在

**调试建议：**
1. 使用 \`console.log()\` 输出关键变量值
2. 检查浏览器开发者工具的错误信息
3. 逐步执行代码，定位问题所在

请分享您遇到的具体错误信息，我可以提供更精准的解决方案。`;
        }

        if (lowerMessage.includes('优化') || lowerMessage.includes('性能') || lowerMessage.includes('改进')) {
            return `代码优化建议：

**性能优化方向：**
- **算法优化** - 选择更高效的算法和数据结构
- **内存管理** - 避免内存泄漏，及时清理不用的变量
- **DOM操作** - 减少DOM查询，批量更新DOM

**代码质量提升：**
- 使用有意义的变量名和函数名
- 添加适当的注释和文档
- 遵循一致的代码风格
- 模块化设计，提高代码复用性

请提供您想要优化的具体代码，我可以给出针对性的建议。`;
        }

        return `感谢您的问题！我是YS Code Editor的AI助手，我可以帮助您：

🔍 **代码分析** - 解释代码逻辑和功能
🛠️ **代码生成** - 根据需求生成代码片段
🐛 **错误调试** - 找出并修复代码问题
📈 **性能优化** - 提升代码执行效率
📚 **最佳实践** - 分享编程经验和技巧

请告诉我您具体需要什么帮助，或者分享您的代码，我会提供更精准的建议！`;
    }

    handleAIAction(action) {
        const currentCode = this.getCurrentCode();

        switch (action) {
            case 'explain-code':
                if (currentCode) {
                    this.addAIMessage(`请解释这段代码：\n\`\`\`\n${currentCode}\n\`\`\``, 'user');
                    this.showAIThinking();
                    setTimeout(() => {
                        const thinkingElement = document.getElementById('ai-thinking');
                        if (thinkingElement) {
                            thinkingElement.closest('.ai-message').remove();
                        }
                        this.addAIMessage(this.explainCode(currentCode), 'system');
                    }, 1500);
                } else {
                    this.addAIMessage('请先打开一个文件或选择一些代码。', 'system');
                }
                break;

            case 'optimize-code':
                if (currentCode) {
                    this.addAIMessage(`请优化这段代码：\n\`\`\`\n${currentCode}\n\`\`\``, 'user');
                    this.showAIThinking();
                    setTimeout(() => {
                        const thinkingElement = document.getElementById('ai-thinking');
                        if (thinkingElement) {
                            thinkingElement.closest('.ai-message').remove();
                        }
                        this.addAIMessage(this.optimizeCode(currentCode), 'system');
                    }, 2000);
                } else {
                    this.addAIMessage('请先打开一个文件或选择一些代码。', 'system');
                }
                break;

            case 'find-bugs':
                if (currentCode) {
                    this.addAIMessage(`请检查这段代码中的错误：\n\`\`\`\n${currentCode}\n\`\`\``, 'user');
                    this.showAIThinking();
                    setTimeout(() => {
                        const thinkingElement = document.getElementById('ai-thinking');
                        if (thinkingElement) {
                            thinkingElement.closest('.ai-message').remove();
                        }
                        this.addAIMessage(this.findBugs(currentCode), 'system');
                    }, 1800);
                } else {
                    this.addAIMessage('请先打开一个文件或选择一些代码。', 'system');
                }
                break;

            case 'generate-docs':
                if (currentCode) {
                    this.addAIMessage(`请为这段代码生成文档：\n\`\`\`\n${currentCode}\n\`\`\``, 'user');
                    this.showAIThinking();
                    setTimeout(() => {
                        const thinkingElement = document.getElementById('ai-thinking');
                        if (thinkingElement) {
                            thinkingElement.closest('.ai-message').remove();
                        }
                        this.addAIMessage(this.generateDocs(currentCode), 'system');
                    }, 1600);
                } else {
                    this.addAIMessage('请先打开一个文件或选择一些代码。', 'system');
                }
                break;
        }
    }

    getCurrentCode() {
        if (this.currentFile === 'welcome') return '';

        const activeEditor = document.querySelector('.code-editor');
        if (activeEditor) {
            const selectedText = activeEditor.value.substring(
                activeEditor.selectionStart,
                activeEditor.selectionEnd
            );
            return selectedText || activeEditor.value;
        }
        return '';
    }

    explainCode(code) {
        return `**代码解释：**

\`\`\`
${code}
\`\`\`

这段代码的主要功能：
- 定义了变量和函数
- 实现了特定的业务逻辑
- 使用了相关的编程概念

**详细分析：**
1. **结构分析** - 代码组织清晰，逻辑合理
2. **功能说明** - 实现了预期的功能需求
3. **技术要点** - 运用了相应的编程技巧

如需更详细的解释，请告诉我您想了解的具体部分。`;
    }

    optimizeCode(code) {
        return `**代码优化建议：**

**原代码：**
\`\`\`
${code}
\`\`\`

**优化方向：**
1. **性能优化** - 减少不必要的计算和DOM操作
2. **代码简化** - 使用更简洁的语法和方法
3. **可读性提升** - 改善变量命名和代码结构

**建议的改进：**
- 使用更高效的算法
- 减少重复代码
- 添加错误处理
- 优化内存使用

具体的优化方案需要根据代码的实际用途来制定。`;
    }

    findBugs(code) {
        return `**代码检查结果：**

\`\`\`
${code}
\`\`\`

**潜在问题：**
1. **语法检查** - 检查是否有语法错误
2. **逻辑验证** - 验证业务逻辑是否正确
3. **边界情况** - 考虑异常输入的处理

**建议修复：**
- 添加输入验证
- 完善错误处理
- 检查变量作用域
- 确保资源正确释放

请提供更多上下文信息，我可以给出更精确的错误分析。`;
    }

    generateDocs(code) {
        return `**代码文档：**

\`\`\`
${code}
\`\`\`

**函数/方法说明：**
- **功能描述：** 实现特定的业务逻辑
- **参数说明：** 接收相应的输入参数
- **返回值：** 返回处理结果
- **使用示例：** 提供调用示例

**注释建议：**
\`\`\`javascript
/**
 * 函数描述
 * @param {type} param - 参数说明
 * @returns {type} 返回值说明
 */
\`\`\`

建议为关键函数添加详细的JSDoc注释。`;
    }

    attachCurrentFile() {
        if (this.currentFile === 'welcome') {
            this.addAIMessage('当前没有打开的文件可以附加。', 'system');
            return;
        }

        const currentCode = this.getCurrentCode() || this.files[this.currentFile] || '';
        const aiInput = document.getElementById('aiInput');

        const attachedText = `[已附加文件: ${this.currentFile}]\n\n${currentCode}\n\n`;
        aiInput.value = attachedText + aiInput.value;
        aiInput.focus();
    }

    // AI面板控制方法
    toggleAIPanel() {
        const aiPanel = document.getElementById('aiAssistantPanel');
        const splitter = document.getElementById('verticalSplitter');
        const toggleButton = document.getElementById('toggleAI');
        const expandButton = document.getElementById('aiExpandBtn');
        const icon = toggleButton.querySelector('i');

        if (this.aiPanelVisible) {
            aiPanel.classList.add('hidden');
            aiPanel.style.width = '0';
            aiPanel.style.minWidth = '0';
            splitter.style.display = 'none';
            icon.className = 'fas fa-chevron-left';
            toggleButton.title = '显示AI助手';
            expandButton.style.display = 'flex';
            this.aiPanelVisible = false;
        } else {
            aiPanel.classList.remove('hidden');
            aiPanel.style.width = this.aiPanelWidth + 'px';
            aiPanel.style.minWidth = '250px';
            splitter.style.display = 'flex';
            icon.className = 'fas fa-chevron-right';
            toggleButton.title = '隐藏AI助手';
            expandButton.style.display = 'none';
            this.aiPanelVisible = true;
        }
    }

    refreshAIChat() {
        // 重新加载AI助手，保持当前对话
        console.log('刷新AI对话');
    }

    clearAIChat() {
        const messagesContainer = document.getElementById('aiChatMessages');
        messagesContainer.innerHTML = `
            <div class="ai-message ai-system">
                <div class="ai-avatar">
                    <i class="fas fa-robot"></i>
                </div>
                <div class="ai-content">
                    <p>你好！我是YS Code Editor的AI代码助手。我可以帮助您：</p>
                    <ul>
                        <li>🔍 分析和解释代码</li>
                        <li>🛠️ 生成代码片段</li>
                        <li>🐛 调试和修复错误</li>
                        <li>📝 优化代码性能</li>
                        <li>💡 提供编程建议</li>
                    </ul>
                    <p>请告诉我您需要什么帮助！</p>
                </div>
            </div>
        `;
        this.aiChatHistory = [];
    }

    setupVerticalSplitter() {
        const splitter = document.getElementById('verticalSplitter');
        const aiPanel = document.getElementById('aiAssistantPanel');
        let isDragging = false;

        splitter.addEventListener('mousedown', (e) => {
            isDragging = true;
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';
            splitter.classList.add('dragging');
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const workspace = document.querySelector('.main-workspace');
            const rect = workspace.getBoundingClientRect();
            const newWidth = rect.right - e.clientX;

            // 限制AI面板的最小和最大宽度
            const minWidth = 250;
            const maxWidth = rect.width * 0.6;

            if (newWidth >= minWidth && newWidth <= maxWidth) {
                aiPanel.style.width = newWidth + 'px';
                this.aiPanelWidth = newWidth;
                this.saveAIPanelWidth(newWidth);
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
                splitter.classList.remove('dragging');
            }
        });

        // 双击分割条重置AI面板宽度
        splitter.addEventListener('dblclick', () => {
            const defaultWidth = 300;
            aiPanel.style.width = defaultWidth + 'px';
            this.aiPanelWidth = defaultWidth;
            this.saveAIPanelWidth(defaultWidth);
        });

        // 初始化AI面板宽度
        aiPanel.style.width = this.aiPanelWidth + 'px';
    }

    // 侧边栏分割条拖拽功能
    setupSidebarSplitter() {
        const splitter = document.getElementById('sidebarSplitter');
        const sidebar = document.querySelector('.sidebar');
        let isDragging = false;

        splitter.addEventListener('mousedown', (e) => {
            isDragging = true;
            document.body.style.cursor = 'col-resize';
            document.body.style.userSelect = 'none';
            splitter.classList.add('dragging');
            e.preventDefault();
        });

        document.addEventListener('mousemove', (e) => {
            if (!isDragging) return;

            const mainContent = document.querySelector('.main-content');
            const rect = mainContent.getBoundingClientRect();
            const activityBarWidth = 48; // 活动栏宽度
            const newWidth = e.clientX - rect.left - activityBarWidth;

            // 限制侧边栏的最小和最大宽度
            const minWidth = 200;
            const maxWidth = Math.min(400, window.innerWidth * 0.4);

            if (newWidth >= minWidth && newWidth <= maxWidth) {
                sidebar.style.width = newWidth + 'px';
                this.sidebarWidth = newWidth;
                this.saveSidebarWidth(newWidth);
            }
        });

        document.addEventListener('mouseup', () => {
            if (isDragging) {
                isDragging = false;
                document.body.style.cursor = '';
                document.body.style.userSelect = '';
                splitter.classList.remove('dragging');
            }
        });

        // 双击分割条重置侧边栏宽度
        splitter.addEventListener('dblclick', () => {
            const defaultWidth = 250;
            sidebar.style.width = defaultWidth + 'px';
            this.sidebarWidth = defaultWidth;
            this.saveSidebarWidth(defaultWidth);
        });

        // 初始化侧边栏宽度
        sidebar.style.width = this.sidebarWidth + 'px';
    }

    // 加载保存的侧边栏宽度
    loadSidebarWidth() {
        const saved = localStorage.getItem('vscode-sidebar-width');
        return saved ? parseInt(saved) : 250;
    }

    // 保存侧边栏宽度
    saveSidebarWidth(width) {
        localStorage.setItem('vscode-sidebar-width', width.toString());
    }

    // 加载保存的终端高度
    loadTerminalHeight() {
        const saved = localStorage.getItem('vscode-terminal-height');
        return saved ? parseInt(saved) : 200;
    }

    // 保存终端高度
    saveTerminalHeight(height) {
        localStorage.setItem('vscode-terminal-height', height.toString());
    }

    // 加载保存的AI面板宽度
    loadAIPanelWidth() {
        const saved = localStorage.getItem('vscode-ai-panel-width');
        return saved ? parseInt(saved) : 300;
    }

    // 保存AI面板宽度
    saveAIPanelWidth(width) {
        localStorage.setItem('vscode-ai-panel-width', width.toString());
    }

    // 加载保存的缩放级别
    loadZoomLevel() {
        const saved = localStorage.getItem('vscode-zoom-level');
        return saved ? parseFloat(saved) : 1.0;
    }

    // 保存缩放级别
    saveZoomLevel(level) {
        localStorage.setItem('vscode-zoom-level', level.toString());
    }

    // 初始化缩放级别
    initializeZoom() {
        // 设置状态栏点击事件
        const zoomDisplay = document.getElementById('zoomLevel');
        if (zoomDisplay) {
            zoomDisplay.addEventListener('click', () => {
                this.resetZoom();
            });
        }

        // 应用保存的缩放级别
        if (this.zoomLevel !== 1.0) {
            const fontSize = Math.round(14 * this.zoomLevel);
            this.applyZoomToAllEditors(fontSize);
            console.log(`应用保存的缩放级别: ${(this.zoomLevel * 100).toFixed(0)}%, 字体大小: ${fontSize}px`);
        }

        // 更新状态栏显示
        this.updateZoomDisplay();
    }

    // 更新缩放级别显示
    updateZoomDisplay() {
        const zoomDisplay = document.getElementById('zoomLevel');
        if (zoomDisplay) {
            zoomDisplay.textContent = `${(this.zoomLevel * 100).toFixed(0)}%`;
        }
    }

    // 切换侧边栏显示/隐藏
    toggleSidebar() {
        const sidebar = document.querySelector('.sidebar');
        const splitter = document.getElementById('sidebarSplitter');
        const isHidden = sidebar.style.width === '0px' || sidebar.style.display === 'none';

        if (isHidden) {
            // 显示侧边栏
            sidebar.style.display = 'block';
            sidebar.style.width = this.sidebarWidth + 'px';
            splitter.style.display = 'flex';
        } else {
            // 隐藏侧边栏
            sidebar.style.display = 'none';
            splitter.style.display = 'none';
        }
    }

    // 重置所有面板大小
    resetAllPanelSizes() {
        // 重置侧边栏
        const sidebar = document.querySelector('.sidebar');
        const defaultSidebarWidth = 250;
        sidebar.style.width = defaultSidebarWidth + 'px';
        this.sidebarWidth = defaultSidebarWidth;
        this.saveSidebarWidth(defaultSidebarWidth);

        // 重置终端
        const terminalPanel = document.getElementById('terminalPanel');
        const defaultTerminalHeight = 200;
        terminalPanel.style.height = defaultTerminalHeight + 'px';
        this.terminalHeight = defaultTerminalHeight;
        this.saveTerminalHeight(defaultTerminalHeight);

        // 重置AI面板
        const aiPanel = document.getElementById('aiAssistantPanel');
        const defaultAIWidth = 300;
        aiPanel.style.width = defaultAIWidth + 'px';
        this.aiPanelWidth = defaultAIWidth;
        this.saveAIPanelWidth(defaultAIWidth);

        console.log('所有面板大小已重置为默认值');
    }
}

// 初始化编辑器
document.addEventListener('DOMContentLoaded', () => {
    new VSCodeEditor();
});
